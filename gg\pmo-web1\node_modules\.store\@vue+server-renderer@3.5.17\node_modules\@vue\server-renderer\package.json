{"name": "@vue/server-renderer", "version": "3.5.17", "description": "@vue/server-renderer", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "types": "dist/server-renderer.d.ts", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/server-renderer.d.ts", "node": {"production": "./dist/server-renderer.cjs.prod.js", "development": "./dist/server-renderer.cjs.js", "default": "./index.js"}, "module": "./dist/server-renderer.esm-bundler.js", "import": "./dist/server-renderer.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "peerDependencies": {"vue": "3.5.17"}, "dependencies": {"@vue/shared": "3.5.17", "@vue/compiler-ssr": "3.5.17"}, "__npminstall_done": true, "_from": "@vue/server-renderer@3.5.17", "_resolved": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.17.tgz"}