{"version": 3, "file": "index.js", "sources": ["../../src/util.ts", "../../src/rule/required.ts", "../../src/rule/whitespace.ts", "../../src/rule/url.ts", "../../src/rule/type.ts", "../../src/rule/range.ts", "../../src/rule/enum.ts", "../../src/rule/pattern.ts", "../../src/rule/index.ts", "../../src/validator/string.ts", "../../src/validator/method.ts", "../../src/validator/number.ts", "../../src/validator/boolean.ts", "../../src/validator/regexp.ts", "../../src/validator/integer.ts", "../../src/validator/float.ts", "../../src/validator/array.ts", "../../src/validator/object.ts", "../../src/validator/enum.ts", "../../src/validator/pattern.ts", "../../src/validator/date.ts", "../../src/validator/required.ts", "../../src/validator/type.ts", "../../src/validator/any.ts", "../../src/validator/index.ts", "../../src/messages.ts", "../../src/index.ts"], "sourcesContent": ["/* eslint no-console:0 */\n\nimport {\n  ValidateError,\n  ValidateOption,\n  RuleValuePackage,\n  InternalRuleItem,\n  SyncErrorType,\n  RuleType,\n  Value,\n  Values,\n} from './interface';\n\nconst formatRegExp = /%[sdj%]/g;\n\ndeclare var ASYNC_VALIDATOR_NO_WARNING;\n\nexport let warning: (type: string, errors: SyncErrorType[]) => void = () => {};\n\n// don't print warning message when in production env or node runtime\nif (\n  typeof process !== 'undefined' &&\n  process.env &&\n  process.env.NODE_ENV !== 'production' &&\n  typeof window !== 'undefined' &&\n  typeof document !== 'undefined'\n) {\n  warning = (type, errors) => {\n    if (\n      typeof console !== 'undefined' &&\n      console.warn &&\n      typeof ASYNC_VALIDATOR_NO_WARNING === 'undefined'\n    ) {\n      if (errors.every(e => typeof e === 'string')) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\n\nexport function convertFieldsError(\n  errors: ValidateError[],\n): Record<string, ValidateError[]> {\n  if (!errors || !errors.length) return null;\n  const fields = {};\n  errors.forEach(error => {\n    const field = error.field;\n    fields[field] = fields[field] || [];\n    fields[field].push(error);\n  });\n  return fields;\n}\n\nexport function format(\n  template: ((...args: any[]) => string) | string,\n  ...args: any[]\n): string {\n  let i = 0;\n  const len = args.length;\n  if (typeof template === 'function') {\n    return template.apply(null, args);\n  }\n  if (typeof template === 'string') {\n    let str = template.replace(formatRegExp, x => {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return (Number(args[i++]) as unknown) as string;\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    return str;\n  }\n  return template;\n}\n\nfunction isNativeStringType(type: string) {\n  return (\n    type === 'string' ||\n    type === 'url' ||\n    type === 'hex' ||\n    type === 'email' ||\n    type === 'date' ||\n    type === 'pattern'\n  );\n}\n\nexport function isEmptyValue(value: Value, type?: string) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\n\nexport function isEmptyObject(obj: object) {\n  return Object.keys(obj).length === 0;\n}\n\nfunction asyncParallelArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  const results: ValidateError[] = [];\n  let total = 0;\n  const arrLength = arr.length;\n\n  function count(errors: ValidateError[]) {\n    results.push(...(errors || []));\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n\n  arr.forEach(a => {\n    func(a, count);\n  });\n}\n\nfunction asyncSerialArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  let index = 0;\n  const arrLength = arr.length;\n\n  function next(errors: ValidateError[]) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    const original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n\n  next([]);\n}\n\nfunction flattenObjArr(objArr: Record<string, RuleValuePackage[]>) {\n  const ret: RuleValuePackage[] = [];\n  Object.keys(objArr).forEach(k => {\n    ret.push(...(objArr[k] || []));\n  });\n  return ret;\n}\n\nexport class AsyncValidationError extends Error {\n  errors: ValidateError[];\n  fields: Record<string, ValidateError[]>;\n\n  constructor(\n    errors: ValidateError[],\n    fields: Record<string, ValidateError[]>,\n  ) {\n    super('Async Validation Error');\n    this.errors = errors;\n    this.fields = fields;\n  }\n}\n\ntype ValidateFunc = (\n  data: RuleValuePackage,\n  doIt: (errors: ValidateError[]) => void,\n) => void;\n\nexport function asyncMap(\n  objArr: Record<string, RuleValuePackage[]>,\n  option: ValidateOption,\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n  source: Values,\n): Promise<Values> {\n  if (option.first) {\n    const pending = new Promise<Values>((resolve, reject) => {\n      const next = (errors: ValidateError[]) => {\n        callback(errors);\n        return errors.length\n          ? reject(new AsyncValidationError(errors, convertFieldsError(errors)))\n          : resolve(source);\n      };\n      const flattenArr = flattenObjArr(objArr);\n      asyncSerialArray(flattenArr, func, next);\n    });\n    pending.catch(e => e);\n    return pending;\n  }\n  const firstFields =\n    option.firstFields === true\n      ? Object.keys(objArr)\n      : option.firstFields || [];\n\n  const objArrKeys = Object.keys(objArr);\n  const objArrLength = objArrKeys.length;\n  let total = 0;\n  const results: ValidateError[] = [];\n  const pending = new Promise<Values>((resolve, reject) => {\n    const next = (errors: ValidateError[]) => {\n      results.push.apply(results, errors);\n      total++;\n      if (total === objArrLength) {\n        callback(results);\n        return results.length\n          ? reject(\n              new AsyncValidationError(results, convertFieldsError(results)),\n            )\n          : resolve(source);\n      }\n    };\n    if (!objArrKeys.length) {\n      callback(results);\n      resolve(source);\n    }\n    objArrKeys.forEach(key => {\n      const arr = objArr[key];\n      if (firstFields.indexOf(key) !== -1) {\n        asyncSerialArray(arr, func, next);\n      } else {\n        asyncParallelArray(arr, func, next);\n      }\n    });\n  });\n  pending.catch(e => e);\n  return pending;\n}\n\nfunction isErrorObj(\n  obj: ValidateError | string | (() => string),\n): obj is ValidateError {\n  return !!(obj && (obj as ValidateError).message !== undefined);\n}\n\nfunction getValue(value: Values, path: string[]) {\n  let v = value;\n  for (let i = 0; i < path.length; i++) {\n    if (v == undefined) {\n      return v;\n    }\n    v = v[path[i]];\n  }\n  return v;\n}\n\nexport function complementError(rule: InternalRuleItem, source: Values) {\n  return (oe: ValidateError | (() => string) | string): ValidateError => {\n    let fieldValue;\n    if (rule.fullFields) {\n      fieldValue = getValue(source, rule.fullFields);\n    } else {\n      fieldValue = source[(oe as any).field || rule.fullField];\n    }\n    if (isErrorObj(oe)) {\n      oe.field = oe.field || rule.fullField;\n      oe.fieldValue = fieldValue;\n      return oe;\n    }\n    return {\n      message: typeof oe === 'function' ? oe() : oe,\n      fieldValue,\n      field: ((oe as unknown) as ValidateError).field || rule.fullField,\n    };\n  };\n}\n\nexport function deepMerge<T extends object>(target: T, source: Partial<T>): T {\n  if (source) {\n    for (const s in source) {\n      if (source.hasOwnProperty(s)) {\n        const value = source[s];\n        if (typeof value === 'object' && typeof target[s] === 'object') {\n          target[s] = {\n            ...target[s],\n            ...value,\n          };\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\n", "import { ExecuteRule } from '../interface';\nimport { format, isEmptyValue } from '../util';\n\nconst required: ExecuteRule = (rule, value, source, errors, options, type) => {\n  if (\n    rule.required &&\n    (!source.hasOwnProperty(rule.field) ||\n      isEmptyValue(value, type || rule.type))\n  ) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\n\nexport default required;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nconst whitespace: ExecuteRule = (rule, value, source, errors, options) => {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\n\nexport default whitespace;\n", "// https://github.com/kevva/url-regex/blob/master/index.js\nlet urlReg: RegExp;\n\nexport default () => {\n  if (urlReg) {\n    return urlReg;\n  }\n\n  const word = '[a-fA-F\\\\d:]';\n  const b = options =>\n    options && options.includeBoundaries\n      ? `(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))`\n      : '';\n\n  const v4 =\n    '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n\n  const v6seg = '[a-fA-F\\\\d]{1,4}';\n  const v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`\n    .replace(/\\s*\\/\\/.*$/gm, '')\n    .replace(/\\n/g, '')\n    .trim();\n\n  // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n  const v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\n  const v4exact = new RegExp(`^${v4}$`);\n  const v6exact = new RegExp(`^${v6}$`);\n\n  const ip = options =>\n    options && options.exact\n      ? v46Exact\n      : new RegExp(\n          `(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(\n            options,\n          )})`,\n          'g',\n        );\n\n  ip.v4 = (options?) =>\n    options && options.exact\n      ? v4exact\n      : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\n  ip.v6 = (options?) =>\n    options && options.exact\n      ? v6exact\n      : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\n\n  const protocol = `(?:(?:[a-z]+:)?//)`;\n  const auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  const ipv4 = ip.v4().source;\n  const ipv6 = ip.v6().source;\n  const host = '(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)';\n  const domain =\n    '(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*';\n  const tld = `(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))`;\n  const port = '(?::\\\\d{2,5})?';\n  const path = '(?:[/?#][^\\\\s\"]*)?';\n  const regex = `(?:${protocol}|www\\\\.)${auth}(?:localhost|${ipv4}|${ipv6}|${host}${domain}${tld})${port}${path}`;\n  urlReg = new RegExp(`(?:^${regex}$)`, 'i');\n  return urlReg;\n};\n", "import { ExecuteRule, Value } from '../interface';\nimport { format } from '../util';\nimport required from './required';\nimport getUrlRegex from './url';\n/* eslint max-len:0 */\n\nconst pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,\n};\n\nconst types = {\n  integer(value: Value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float(value: Value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array(value: Value) {\n    return Array.isArray(value);\n  },\n  regexp(value: Value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date(value: Value) {\n    return (\n      typeof value.getTime === 'function' &&\n      typeof value.getMonth === 'function' &&\n      typeof value.getYear === 'function' &&\n      !isNaN(value.getTime())\n    );\n  },\n  number(value: Value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object(value: Value) {\n    return typeof value === 'object' && !types.array(value);\n  },\n  method(value: Value) {\n    return typeof value === 'function';\n  },\n  email(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 320 &&\n      !!value.match(pattern.email)\n    );\n  },\n  url(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 2048 &&\n      !!value.match(getUrlRegex())\n    );\n  },\n  hex(value: Value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  },\n};\n\nconst type: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  const custom = [\n    'integer',\n    'float',\n    'array',\n    'regexp',\n    'object',\n    'method',\n    'email',\n    'number',\n    'date',\n    'url',\n    'hex',\n  ];\n  const ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(\n        format(options.messages.types[ruleType], rule.fullField, rule.type),\n      );\n    }\n    // straight typeof check\n  } else if (ruleType && typeof value !== rule.type) {\n    errors.push(\n      format(options.messages.types[ruleType], rule.fullField, rule.type),\n    );\n  }\n};\n\nexport default type;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst range: ExecuteRule = (rule, value, source, errors, options) => {\n  const len = typeof rule.len === 'number';\n  const min = typeof rule.min === 'number';\n  const max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  const spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  let val = value;\n  let key = null;\n  const num = typeof value === 'number';\n  const str = typeof value === 'string';\n  const arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(\n      format(options.messages[key].range, rule.fullField, rule.min, rule.max),\n    );\n  }\n};\n\nexport default range;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteRule = (rule, value, source, errors, options) => {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(\n      format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')),\n    );\n  }\n};\n\nexport default enumerable;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst pattern: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    } else if (typeof rule.pattern === 'string') {\n      const _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    }\n  }\n};\n\nexport default pattern;\n", "import required from './required';\nimport whitespace from './whitespace';\nimport type from './type';\nimport range from './range';\nimport enumRule from './enum';\nimport pattern from './pattern';\n\nexport default {\n  required,\n  whitespace,\n  type,\n  range,\n  enum: enumRule,\n  pattern,\n};\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst string: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default string;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst method: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default method;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst number: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (value === '') {\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default number;\n", "import { isEmptyValue } from '../util';\nimport rules from '../rule';\nimport { ExecuteValidator } from '../interface';\n\nconst boolean: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default boolean;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst regexp: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default regexp;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst integer: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default integer;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst floatFn: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default floatFn;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule/index';\n\nconst array: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default array;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst object: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default object;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteValidator = (\n  rule,\n  value,\n  callback,\n  source,\n  options,\n) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default enumerable;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst pattern: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default pattern;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst date: ExecuteValidator = (rule, value, callback, source, options) => {\n  // console.log('integer rule called %j', rule);\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      let dateObject;\n\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default date;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\n\nconst required: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const type = Array.isArray(value) ? 'array' : typeof value;\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\n\nexport default required;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst type: ExecuteValidator = (rule, value, callback, source, options) => {\n  const ruleType = rule.type;\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default type;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst any: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\n\nexport default any;\n", "import string from './string';\nimport method from './method';\nimport number from './number';\nimport boolean from './boolean';\nimport regexp from './regexp';\nimport integer from './integer';\nimport float from './float';\nimport array from './array';\nimport object from './object';\nimport enumValidator from './enum';\nimport pattern from './pattern';\nimport date from './date';\nimport required from './required';\nimport type from './type';\nimport any from './any';\n\nexport default {\n  string,\n  method,\n  number,\n  boolean,\n  regexp,\n  integer,\n  float,\n  array,\n  object,\n  enum: enumValidator,\n  pattern,\n  date,\n  url: type,\n  hex: type,\n  email: type,\n  required,\n  any,\n};\n", "import { InternalValidateMessages } from './interface';\n\nexport function newMessages(): InternalValidateMessages {\n  return {\n    default: 'Validation error on field %s',\n    required: '%s is required',\n    enum: '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid',\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      boolean: '%s is not a %s',\n      integer: '%s is not an %s',\n      float: '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s',\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters',\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s',\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length',\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s',\n    },\n    clone() {\n      const cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    },\n  };\n}\n\nexport const messages = newMessages();\n", "import {\n  format,\n  complementError,\n  asyncMap,\n  warning,\n  deepMerge,\n  convertFieldsError,\n} from './util';\nimport validators from './validator/index';\nimport { messages as defaultMessages, newMessages } from './messages';\nimport {\n  InternalRuleItem,\n  InternalValidateMessages,\n  Rule,\n  RuleItem,\n  Rules,\n  ValidateCallback,\n  ValidateMessages,\n  ValidateOption,\n  Values,\n  RuleValuePackage,\n  ValidateError,\n  ValidateFieldsError,\n  SyncErrorType,\n  ValidateResult,\n} from './interface';\n\nexport * from './interface';\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nclass Schema {\n  // ========================= Static =========================\n  static register = function register(type: string, validator) {\n    if (typeof validator !== 'function') {\n      throw new Error(\n        'Cannot register a validator by type, validator is not a function',\n      );\n    }\n    validators[type] = validator;\n  };\n\n  static warning = warning;\n\n  static messages = defaultMessages;\n\n  static validators = validators;\n\n  // ======================== Instance ========================\n  rules: Record<string, RuleItem[]> = null;\n  _messages: InternalValidateMessages = defaultMessages;\n\n  constructor(descriptor: Rules) {\n    this.define(descriptor);\n  }\n\n  define(rules: Rules) {\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if (typeof rules !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n\n    Object.keys(rules).forEach(name => {\n      const item: Rule = rules[name];\n      this.rules[name] = Array.isArray(item) ? item : [item];\n    });\n  }\n\n  messages(messages?: ValidateMessages) {\n    if (messages) {\n      this._messages = deepMerge(newMessages(), messages);\n    }\n    return this._messages;\n  }\n\n  validate(\n    source: Values,\n    option?: ValidateOption,\n    callback?: ValidateCallback,\n  ): Promise<Values>;\n  validate(source: Values, callback: ValidateCallback): Promise<Values>;\n  validate(source: Values): Promise<Values>;\n\n  validate(source_: Values, o: any = {}, oc: any = () => {}): Promise<Values> {\n    let source: Values = source_;\n    let options: ValidateOption = o;\n    let callback: ValidateCallback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback(null, source);\n      }\n      return Promise.resolve(source);\n    }\n\n    function complete(results: (ValidateError | ValidateError[])[]) {\n      let errors: ValidateError[] = [];\n      let fields: ValidateFieldsError = {};\n\n      function add(e: ValidateError | ValidateError[]) {\n        if (Array.isArray(e)) {\n          errors = errors.concat(...e);\n        } else {\n          errors.push(e);\n        }\n      }\n\n      for (let i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        callback(null, source);\n      } else {\n        fields = convertFieldsError(errors);\n        (callback as (\n          errors: ValidateError[],\n          fields: ValidateFieldsError,\n        ) => void)(errors, fields);\n      }\n    }\n\n    if (options.messages) {\n      let messages = this.messages();\n      if (messages === defaultMessages) {\n        messages = newMessages();\n      }\n      deepMerge(messages, options.messages);\n      options.messages = messages;\n    } else {\n      options.messages = this.messages();\n    }\n\n    const series: Record<string, RuleValuePackage[]> = {};\n    const keys = options.keys || Object.keys(this.rules);\n    keys.forEach(z => {\n      const arr = this.rules[z];\n      let value = source[z];\n      arr.forEach(r => {\n        let rule: InternalRuleItem = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = { ...source };\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule,\n          };\n        } else {\n          rule = { ...rule };\n        }\n\n        // Fill validator. Skip if nothing need to validate\n        rule.validator = this.getValidationMethod(rule);\n        if (!rule.validator) {\n          return;\n        }\n\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = this.getType(rule);\n        series[z] = series[z] || [];\n        series[z].push({\n          rule,\n          value,\n          source,\n          field: z,\n        });\n      });\n    });\n    const errorFields = {};\n    return asyncMap(\n      series,\n      options,\n      (data, doIt) => {\n        const rule = data.rule;\n        let deep =\n          (rule.type === 'object' || rule.type === 'array') &&\n          (typeof rule.fields === 'object' ||\n            typeof rule.defaultField === 'object');\n        deep = deep && (rule.required || (!rule.required && data.value));\n        rule.field = data.field;\n\n        function addFullField(key: string, schema: RuleItem) {\n          return {\n            ...schema,\n            fullField: `${rule.fullField}.${key}`,\n            fullFields: rule.fullFields ? [...rule.fullFields, key] : [key],\n          };\n        }\n\n        function cb(e: SyncErrorType | SyncErrorType[] = []) {\n          let errorList = Array.isArray(e) ? e : [e];\n          if (!options.suppressWarning && errorList.length) {\n            Schema.warning('async-validator:', errorList);\n          }\n          if (errorList.length && rule.message !== undefined) {\n            errorList = [].concat(rule.message);\n          }\n\n          // Fill error info\n          let filledErrors = errorList.map(complementError(rule, source));\n\n          if (options.first && filledErrors.length) {\n            errorFields[rule.field] = 1;\n            return doIt(filledErrors);\n          }\n          if (!deep) {\n            doIt(filledErrors);\n          } else {\n            // if rule is required but the target object\n            // does not exist fail at the rule level and don't\n            // go deeper\n            if (rule.required && !data.value) {\n              if (rule.message !== undefined) {\n                filledErrors = []\n                  .concat(rule.message)\n                  .map(complementError(rule, source));\n              } else if (options.error) {\n                filledErrors = [\n                  options.error(\n                    rule,\n                    format(options.messages.required, rule.field),\n                  ),\n                ];\n              }\n              return doIt(filledErrors);\n            }\n\n            let fieldsSchema: Record<string, Rule> = {};\n            if (rule.defaultField) {\n              Object.keys(data.value).map(key => {\n                fieldsSchema[key] = rule.defaultField;\n              });\n            }\n            fieldsSchema = {\n              ...fieldsSchema,\n              ...data.rule.fields,\n            };\n\n            const paredFieldsSchema: Record<string, RuleItem[]> = {};\n\n            Object.keys(fieldsSchema).forEach(field => {\n              const fieldSchema = fieldsSchema[field];\n              const fieldSchemaList = Array.isArray(fieldSchema)\n                ? fieldSchema\n                : [fieldSchema];\n              paredFieldsSchema[field] = fieldSchemaList.map(\n                addFullField.bind(null, field),\n              );\n            });\n            const schema = new Schema(paredFieldsSchema);\n            schema.messages(options.messages);\n            if (data.rule.options) {\n              data.rule.options.messages = options.messages;\n              data.rule.options.error = options.error;\n            }\n            schema.validate(data.value, data.rule.options || options, errs => {\n              const finalErrors = [];\n              if (filledErrors && filledErrors.length) {\n                finalErrors.push(...filledErrors);\n              }\n              if (errs && errs.length) {\n                finalErrors.push(...errs);\n              }\n              doIt(finalErrors.length ? finalErrors : null);\n            });\n          }\n        }\n\n        let res: ValidateResult;\n        if (rule.asyncValidator) {\n          res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n        } else if (rule.validator) {\n          try {\n            res = rule.validator(rule, data.value, cb, data.source, options);\n          } catch (error) {\n            console.error?.(error);\n            // rethrow to report error\n            if (!options.suppressValidatorError) {\n              setTimeout(() => {\n                throw error;\n              }, 0);\n            }\n            cb(error.message);\n          }\n          if (res === true) {\n            cb();\n          } else if (res === false) {\n            cb(\n              typeof rule.message === 'function'\n                ? rule.message(rule.fullField || rule.field)\n                : rule.message || `${rule.fullField || rule.field} fails`,\n            );\n          } else if (res instanceof Array) {\n            cb(res);\n          } else if (res instanceof Error) {\n            cb(res.message);\n          }\n        }\n        if (res && (res as Promise<void>).then) {\n          (res as Promise<void>).then(\n            () => cb(),\n            e => cb(e),\n          );\n        }\n      },\n      results => {\n        complete(results);\n      },\n      source,\n    );\n  }\n\n  getType(rule: InternalRuleItem) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (\n      typeof rule.validator !== 'function' &&\n      rule.type &&\n      !validators.hasOwnProperty(rule.type)\n    ) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  }\n\n  getValidationMethod(rule: InternalRuleItem) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    const keys = Object.keys(rule);\n    const messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || undefined;\n  }\n}\n\nexport default Schema;\n"], "names": ["formatRegExp", "warning", "process", "env", "NODE_ENV", "window", "document", "type", "errors", "console", "warn", "ASYNC_VALIDATOR_NO_WARNING", "every", "e", "convertFieldsError", "length", "fields", "for<PERSON>ach", "error", "field", "push", "format", "template", "args", "i", "len", "apply", "str", "replace", "x", "String", "Number", "JSON", "stringify", "_", "isNativeStringType", "isEmptyValue", "value", "undefined", "Array", "isArray", "asyncParallelArray", "arr", "func", "callback", "results", "total", "arr<PERSON><PERSON><PERSON>", "count", "a", "asyncSerialArray", "index", "next", "original", "flatten<PERSON>bj<PERSON>rr", "obj<PERSON>rr", "ret", "Object", "keys", "k", "AsyncValidationError", "Error", "asyncMap", "option", "source", "first", "pending", "Promise", "resolve", "reject", "flattenArr", "firstFields", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "isErrorObj", "obj", "message", "getValue", "path", "v", "complementError", "rule", "oe", "fieldValue", "fullFields", "fullField", "deepMerge", "target", "s", "hasOwnProperty", "required", "options", "messages", "whitespace", "test", "urlReg", "word", "b", "includeBoundaries", "v4", "v6seg", "v6", "trim", "v46Exact", "RegExp", "v4exact", "v6exact", "ip", "exact", "protocol", "auth", "ipv4", "ipv6", "host", "domain", "tld", "port", "regex", "pattern", "email", "hex", "types", "integer", "number", "parseInt", "array", "regexp", "date", "getTime", "getMonth", "getYear", "isNaN", "object", "method", "match", "url", "getUrlRegex", "custom", "ruleType", "range", "min", "max", "spRegexp", "val", "num", "ENUM", "enumerable", "join", "lastIndex", "mismatch", "_pattern", "enumRule", "string", "validate", "rules", "boolean", "floatFn", "dateObject", "Date", "any", "float", "enumValidator", "newMessages", "parse", "invalid", "clone", "cloned", "<PERSON><PERSON><PERSON>", "descriptor", "_messages", "defaultMessages", "define", "name", "item", "source_", "o", "oc", "complete", "add", "concat", "series", "z", "r", "transform", "validator", "getValidationMethod", "getType", "errorFields", "data", "doIt", "deep", "defaultField", "addFullField", "schema", "cb", "errorList", "suppressWarning", "filledErrors", "map", "fieldsSchema", "paredFieldsSchema", "fieldSchema", "fieldSchemaList", "bind", "errs", "finalErrors", "res", "asyncValidator", "suppressValidatorError", "setTimeout", "then", "validators", "messageIndex", "splice", "register"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAaA,IAAMA,YAAY,GAAG,UAArB,CAAA;AAIO,IAAIC,OAAwD,GAAG,mBAAM,EAArE;;AAGP,IACE,OAAOC,OAAP,KAAmB,WAAnB,IACAA,OAAO,CAACC,GADR,IAEAD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAFzB,IAGA,OAAOC,MAAP,KAAkB,WAHlB,IAIA,OAAOC,QAAP,KAAoB,WALtB,EAME;AACAL,EAAAA,OAAO,GAAG,SAAA,OAAA,CAACM,IAAD,EAAOC,MAAP,EAAkB;AAC1B,IAAA,IACE,OAAOC,OAAP,KAAmB,WAAnB,IACAA,OAAO,CAACC,IADR,IAEA,OAAOC,0BAAP,KAAsC,WAHxC,EAIE;AACA,MAAA,IAAIH,MAAM,CAACI,KAAP,CAAa,UAAAC,CAAC,EAAA;QAAA,OAAI,OAAOA,CAAP,KAAa,QAAjB,CAAA;AAAA,OAAd,CAAJ,EAA8C;AAC5CJ,QAAAA,OAAO,CAACC,IAAR,CAAaH,IAAb,EAAmBC,MAAnB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;GATH,CAAA;AAWD,CAAA;;AAEM,SAASM,kBAAT,CACLN,MADK,EAE4B;EACjC,IAAI,CAACA,MAAD,IAAW,CAACA,MAAM,CAACO,MAAvB,EAA+B,OAAO,IAAP,CAAA;EAC/B,IAAMC,MAAM,GAAG,EAAf,CAAA;AACAR,EAAAA,MAAM,CAACS,OAAP,CAAe,UAAAC,KAAK,EAAI;AACtB,IAAA,IAAMC,KAAK,GAAGD,KAAK,CAACC,KAApB,CAAA;IACAH,MAAM,CAACG,KAAD,CAAN,GAAgBH,MAAM,CAACG,KAAD,CAAN,IAAiB,EAAjC,CAAA;AACAH,IAAAA,MAAM,CAACG,KAAD,CAAN,CAAcC,IAAd,CAAmBF,KAAnB,CAAA,CAAA;GAHF,CAAA,CAAA;AAKA,EAAA,OAAOF,MAAP,CAAA;AACD,CAAA;AAEM,SAASK,MAAT,CACLC,QADK,EAGG;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EADLC,IACK,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IADLA,IACK,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;;EACR,IAAIC,CAAC,GAAG,CAAR,CAAA;AACA,EAAA,IAAMC,GAAG,GAAGF,IAAI,CAACR,MAAjB,CAAA;;AACA,EAAA,IAAI,OAAOO,QAAP,KAAoB,UAAxB,EAAoC;AAClC,IAAA,OAAOA,QAAQ,CAACI,KAAT,CAAe,IAAf,EAAqBH,IAArB,CAAP,CAAA;AACD,GAAA;;AACD,EAAA,IAAI,OAAOD,QAAP,KAAoB,QAAxB,EAAkC;IAChC,IAAIK,GAAG,GAAGL,QAAQ,CAACM,OAAT,CAAiB5B,YAAjB,EAA+B,UAAA6B,CAAC,EAAI;MAC5C,IAAIA,CAAC,KAAK,IAAV,EAAgB;AACd,QAAA,OAAO,GAAP,CAAA;AACD,OAAA;;MACD,IAAIL,CAAC,IAAIC,GAAT,EAAc;AACZ,QAAA,OAAOI,CAAP,CAAA;AACD,OAAA;;AACD,MAAA,QAAQA,CAAR;AACE,QAAA,KAAK,IAAL;AACE,UAAA,OAAOC,MAAM,CAACP,IAAI,CAACC,CAAC,EAAF,CAAL,CAAb,CAAA;;AACF,QAAA,KAAK,IAAL;AACE,UAAA,OAAQO,MAAM,CAACR,IAAI,CAACC,CAAC,EAAF,CAAL,CAAd,CAAA;;AACF,QAAA,KAAK,IAAL;UACE,IAAI;YACF,OAAOQ,IAAI,CAACC,SAAL,CAAeV,IAAI,CAACC,CAAC,EAAF,CAAnB,CAAP,CAAA;WADF,CAEE,OAAOU,CAAP,EAAU;AACV,YAAA,OAAO,YAAP,CAAA;AACD,WAAA;;AACD,UAAA,MAAA;;AACF,QAAA;AACE,UAAA,OAAOL,CAAP,CAAA;AAbJ,OAAA;AAeD,KAtBS,CAAV,CAAA;AAuBA,IAAA,OAAOF,GAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAOL,QAAP,CAAA;AACD,CAAA;;AAED,SAASa,kBAAT,CAA4B5B,IAA5B,EAA0C;EACxC,OACEA,IAAI,KAAK,QAAT,IACAA,IAAI,KAAK,KADT,IAEAA,IAAI,KAAK,KAFT,IAGAA,IAAI,KAAK,OAHT,IAIAA,IAAI,KAAK,MAJT,IAKAA,IAAI,KAAK,SANX,CAAA;AAQD,CAAA;;AAEM,SAAS6B,YAAT,CAAsBC,KAAtB,EAAoC9B,IAApC,EAAmD;AACxD,EAAA,IAAI8B,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAArC,EAA2C;AACzC,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AACD,EAAA,IAAI9B,IAAI,KAAK,OAAT,IAAoBgC,KAAK,CAACC,OAAN,CAAcH,KAAd,CAApB,IAA4C,CAACA,KAAK,CAACtB,MAAvD,EAA+D;AAC7D,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AACD,EAAA,IAAIoB,kBAAkB,CAAC5B,IAAD,CAAlB,IAA4B,OAAO8B,KAAP,KAAiB,QAA7C,IAAyD,CAACA,KAA9D,EAAqE;AACnE,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAO,KAAP,CAAA;AACD,CAAA;;AAMD,SAASI,kBAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,QAHF,EAIE;EACA,IAAMC,OAAwB,GAAG,EAAjC,CAAA;EACA,IAAIC,KAAK,GAAG,CAAZ,CAAA;AACA,EAAA,IAAMC,SAAS,GAAGL,GAAG,CAAC3B,MAAtB,CAAA;;EAEA,SAASiC,KAAT,CAAexC,MAAf,EAAwC;IACtCqC,OAAO,CAACzB,IAAR,CAAAyB,KAAAA,CAAAA,OAAO,EAAUrC,MAAM,IAAI,EAApB,CAAP,CAAA;IACAsC,KAAK,EAAA,CAAA;;IACL,IAAIA,KAAK,KAAKC,SAAd,EAAyB;MACvBH,QAAQ,CAACC,OAAD,CAAR,CAAA;AACD,KAAA;AACF,GAAA;;AAEDH,EAAAA,GAAG,CAACzB,OAAJ,CAAY,UAAAgC,CAAC,EAAI;AACfN,IAAAA,IAAI,CAACM,CAAD,EAAID,KAAJ,CAAJ,CAAA;GADF,CAAA,CAAA;AAGD,CAAA;;AAED,SAASE,gBAAT,CACER,GADF,EAEEC,IAFF,EAGEC,QAHF,EAIE;EACA,IAAIO,KAAK,GAAG,CAAZ,CAAA;AACA,EAAA,IAAMJ,SAAS,GAAGL,GAAG,CAAC3B,MAAtB,CAAA;;EAEA,SAASqC,IAAT,CAAc5C,MAAd,EAAuC;AACrC,IAAA,IAAIA,MAAM,IAAIA,MAAM,CAACO,MAArB,EAA6B;MAC3B6B,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IACD,IAAM6C,QAAQ,GAAGF,KAAjB,CAAA;IACAA,KAAK,GAAGA,KAAK,GAAG,CAAhB,CAAA;;IACA,IAAIE,QAAQ,GAAGN,SAAf,EAA0B;AACxBJ,MAAAA,IAAI,CAACD,GAAG,CAACW,QAAD,CAAJ,EAAgBD,IAAhB,CAAJ,CAAA;AACD,KAFD,MAEO;MACLR,QAAQ,CAAC,EAAD,CAAR,CAAA;AACD,KAAA;AACF,GAAA;;EAEDQ,IAAI,CAAC,EAAD,CAAJ,CAAA;AACD,CAAA;;AAED,SAASE,aAAT,CAAuBC,MAAvB,EAAmE;EACjE,IAAMC,GAAuB,GAAG,EAAhC,CAAA;EACAC,MAAM,CAACC,IAAP,CAAYH,MAAZ,EAAoBtC,OAApB,CAA4B,UAAA0C,CAAC,EAAI;IAC/BH,GAAG,CAACpC,IAAJ,CAAA,KAAA,CAAAoC,GAAG,EAAUD,MAAM,CAACI,CAAD,CAAN,IAAa,EAAvB,CAAH,CAAA;GADF,CAAA,CAAA;AAGA,EAAA,OAAOH,GAAP,CAAA;AACD,CAAA;;AAED,IAAaI,oBAAb,gBAAA,UAAA,MAAA,EAAA;AAAA,EAAA,cAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;;EAIE,SACEpD,oBAAAA,CAAAA,MADF,EAEEQ,MAFF,EAGE;AAAA,IAAA,IAAA,KAAA,CAAA;;AACA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,wBAAN,CAAA,IAAA,IAAA,CAAA;IACA,KAAKR,CAAAA,MAAL,GAAcA,MAAd,CAAA;IACA,KAAKQ,CAAAA,MAAL,GAAcA,MAAd,CAAA;AAHA,IAAA,OAAA,KAAA,CAAA;AAID,GAAA;;AAXH,EAAA,OAAA,oBAAA,CAAA;AAAA,CAAA,eAAA,gBAAA,CAA0C6C,KAA1C,CAAA,CAAA,CAAA;AAmBO,SAASC,QAAT,CACLP,MADK,EAELQ,MAFK,EAGLpB,IAHK,EAILC,QAJK,EAKLoB,MALK,EAMY;EACjB,IAAID,MAAM,CAACE,KAAX,EAAkB;IAChB,IAAMC,QAAO,GAAG,IAAIC,OAAJ,CAAoB,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACvD,MAAA,IAAMjB,IAAI,GAAG,SAAPA,IAAO,CAAC5C,MAAD,EAA6B;QACxCoC,QAAQ,CAACpC,MAAD,CAAR,CAAA;QACA,OAAOA,MAAM,CAACO,MAAP,GACHsD,MAAM,CAAC,IAAIT,oBAAJ,CAAyBpD,MAAzB,EAAiCM,kBAAkB,CAACN,MAAD,CAAnD,CAAD,CADH,GAEH4D,OAAO,CAACJ,MAAD,CAFX,CAAA;OAFF,CAAA;;AAMA,MAAA,IAAMM,UAAU,GAAGhB,aAAa,CAACC,MAAD,CAAhC,CAAA;AACAL,MAAAA,gBAAgB,CAACoB,UAAD,EAAa3B,IAAb,EAAmBS,IAAnB,CAAhB,CAAA;AACD,KATe,CAAhB,CAAA;;IAUAc,QAAO,CAAA,OAAA,CAAP,CAAc,UAAArD,CAAC,EAAA;AAAA,MAAA,OAAIA,CAAJ,CAAA;KAAf,CAAA,CAAA;;AACA,IAAA,OAAOqD,QAAP,CAAA;AACD,GAAA;;AACD,EAAA,IAAMK,WAAW,GACfR,MAAM,CAACQ,WAAP,KAAuB,IAAvB,GACId,MAAM,CAACC,IAAP,CAAYH,MAAZ,CADJ,GAEIQ,MAAM,CAACQ,WAAP,IAAsB,EAH5B,CAAA;AAKA,EAAA,IAAMC,UAAU,GAAGf,MAAM,CAACC,IAAP,CAAYH,MAAZ,CAAnB,CAAA;AACA,EAAA,IAAMkB,YAAY,GAAGD,UAAU,CAACzD,MAAhC,CAAA;EACA,IAAI+B,KAAK,GAAG,CAAZ,CAAA;EACA,IAAMD,OAAwB,GAAG,EAAjC,CAAA;EACA,IAAMqB,OAAO,GAAG,IAAIC,OAAJ,CAAoB,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACvD,IAAA,IAAMjB,IAAI,GAAG,SAAPA,IAAO,CAAC5C,MAAD,EAA6B;AACxCqC,MAAAA,OAAO,CAACzB,IAAR,CAAaM,KAAb,CAAmBmB,OAAnB,EAA4BrC,MAA5B,CAAA,CAAA;MACAsC,KAAK,EAAA,CAAA;;MACL,IAAIA,KAAK,KAAK2B,YAAd,EAA4B;QAC1B7B,QAAQ,CAACC,OAAD,CAAR,CAAA;QACA,OAAOA,OAAO,CAAC9B,MAAR,GACHsD,MAAM,CACJ,IAAIT,oBAAJ,CAAyBf,OAAzB,EAAkC/B,kBAAkB,CAAC+B,OAAD,CAApD,CADI,CADH,GAIHuB,OAAO,CAACJ,MAAD,CAJX,CAAA;AAKD,OAAA;KAVH,CAAA;;AAYA,IAAA,IAAI,CAACQ,UAAU,CAACzD,MAAhB,EAAwB;MACtB6B,QAAQ,CAACC,OAAD,CAAR,CAAA;MACAuB,OAAO,CAACJ,MAAD,CAAP,CAAA;AACD,KAAA;;AACDQ,IAAAA,UAAU,CAACvD,OAAX,CAAmB,UAAAyD,GAAG,EAAI;AACxB,MAAA,IAAMhC,GAAG,GAAGa,MAAM,CAACmB,GAAD,CAAlB,CAAA;;MACA,IAAIH,WAAW,CAACI,OAAZ,CAAoBD,GAApB,CAA6B,KAAA,CAAC,CAAlC,EAAqC;AACnCxB,QAAAA,gBAAgB,CAACR,GAAD,EAAMC,IAAN,EAAYS,IAAZ,CAAhB,CAAA;AACD,OAFD,MAEO;AACLX,QAAAA,kBAAkB,CAACC,GAAD,EAAMC,IAAN,EAAYS,IAAZ,CAAlB,CAAA;AACD,OAAA;KANH,CAAA,CAAA;AAQD,GAzBe,CAAhB,CAAA;EA0BAc,OAAO,CAAA,OAAA,CAAP,CAAc,UAAArD,CAAC,EAAA;AAAA,IAAA,OAAIA,CAAJ,CAAA;GAAf,CAAA,CAAA;AACA,EAAA,OAAOqD,OAAP,CAAA;AACD,CAAA;;AAED,SAASU,UAAT,CACEC,GADF,EAEwB;EACtB,OAAO,CAAC,EAAEA,GAAG,IAAKA,GAAD,CAAuBC,OAAvB,KAAmCxC,SAA5C,CAAR,CAAA;AACD,CAAA;;AAED,SAASyC,QAAT,CAAkB1C,KAAlB,EAAiC2C,IAAjC,EAAiD;EAC/C,IAAIC,CAAC,GAAG5C,KAAR,CAAA;;AACA,EAAA,KAAK,IAAIb,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwD,IAAI,CAACjE,MAAzB,EAAiCS,CAAC,EAAlC,EAAsC;IACpC,IAAIyD,CAAC,IAAI3C,SAAT,EAAoB;AAClB,MAAA,OAAO2C,CAAP,CAAA;AACD,KAAA;;AACDA,IAAAA,CAAC,GAAGA,CAAC,CAACD,IAAI,CAACxD,CAAD,CAAL,CAAL,CAAA;AACD,GAAA;;AACD,EAAA,OAAOyD,CAAP,CAAA;AACD,CAAA;;AAEM,SAASC,eAAT,CAAyBC,IAAzB,EAAiDnB,MAAjD,EAAiE;EACtE,OAAO,UAACoB,EAAD,EAAgE;AACrE,IAAA,IAAIC,UAAJ,CAAA;;IACA,IAAIF,IAAI,CAACG,UAAT,EAAqB;MACnBD,UAAU,GAAGN,QAAQ,CAACf,MAAD,EAASmB,IAAI,CAACG,UAAd,CAArB,CAAA;AACD,KAFD,MAEO;MACLD,UAAU,GAAGrB,MAAM,CAAEoB,EAAD,CAAYjE,KAAZ,IAAqBgE,IAAI,CAACI,SAA3B,CAAnB,CAAA;AACD,KAAA;;AACD,IAAA,IAAIX,UAAU,CAACQ,EAAD,CAAd,EAAoB;MAClBA,EAAE,CAACjE,KAAH,GAAWiE,EAAE,CAACjE,KAAH,IAAYgE,IAAI,CAACI,SAA5B,CAAA;MACAH,EAAE,CAACC,UAAH,GAAgBA,UAAhB,CAAA;AACA,MAAA,OAAOD,EAAP,CAAA;AACD,KAAA;;IACD,OAAO;MACLN,OAAO,EAAE,OAAOM,EAAP,KAAc,UAAd,GAA2BA,EAAE,EAA7B,GAAkCA,EADtC;AAELC,MAAAA,UAAU,EAAVA,UAFK;AAGLlE,MAAAA,KAAK,EAAIiE,EAAF,CAAmCjE,KAAnC,IAA4CgE,IAAI,CAACI,SAAAA;KAH1D,CAAA;GAZF,CAAA;AAkBD,CAAA;AAEM,SAASC,SAAT,CAAqCC,MAArC,EAAgDzB,MAAhD,EAAuE;AAC5E,EAAA,IAAIA,MAAJ,EAAY;AACV,IAAA,KAAK,IAAM0B,CAAX,IAAgB1B,MAAhB,EAAwB;AACtB,MAAA,IAAIA,MAAM,CAAC2B,cAAP,CAAsBD,CAAtB,CAAJ,EAA8B;AAC5B,QAAA,IAAMrD,KAAK,GAAG2B,MAAM,CAAC0B,CAAD,CAApB,CAAA;;AACA,QAAA,IAAI,OAAOrD,KAAP,KAAiB,QAAjB,IAA6B,OAAOoD,MAAM,CAACC,CAAD,CAAb,KAAqB,QAAtD,EAAgE;UAC9DD,MAAM,CAACC,CAAD,CAAN,GAAA,QAAA,CAAA,EAAA,EACKD,MAAM,CAACC,CAAD,CADX,EAEKrD,KAFL,CAAA,CAAA;AAID,SALD,MAKO;AACLoD,UAAAA,MAAM,CAACC,CAAD,CAAN,GAAYrD,KAAZ,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;;AACD,EAAA,OAAOoD,MAAP,CAAA;AACD;;ACjTD,IAAMG,UAAqB,GAAG,SAAxBA,QAAwB,CAACT,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAAuCtF,IAAvC,EAAgD;EAC5E,IACE4E,IAAI,CAACS,QAAL,KACC,CAAC5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CAAD,IACCiB,YAAY,CAACC,KAAD,EAAQ9B,IAAI,IAAI4E,IAAI,CAAC5E,IAArB,CAFd,CADF,EAIE;AACAC,IAAAA,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBF,QAAlB,EAA4BT,IAAI,CAACI,SAAjC,CAAlB,CAAA,CAAA;AACD,GAAA;AACF,CARD;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMQ,UAAuB,GAAG,SAA1BA,UAA0B,CAACZ,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAA0C;EACxE,IAAI,OAAA,CAAQG,IAAR,CAAa3D,KAAb,KAAuBA,KAAK,KAAK,EAArC,EAAyC;AACvC7B,IAAAA,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBC,UAAlB,EAA8BZ,IAAI,CAACI,SAAnC,CAAlB,CAAA,CAAA;AACD,GAAA;AACF,CAJD;;ACdA;AACA,IAAIU,MAAJ,CAAA;AAEA,kBAAA,CAAe,YAAM;AACnB,EAAA,IAAIA,MAAJ,EAAY;AACV,IAAA,OAAOA,MAAP,CAAA;AACD,GAAA;;EAED,IAAMC,IAAI,GAAG,cAAb,CAAA;;AACA,EAAA,IAAMC,CAAC,GAAG,SAAJA,CAAI,CAAAN,OAAO,EAAA;IAAA,OACfA,OAAO,IAAIA,OAAO,CAACO,iBAAnB,wBACuBF,IADvB,GAAA,QAAA,GACoCA,IADpC,GAAA,aAAA,GAEI,EAHW,CAAA;GAAjB,CAAA;;EAKA,IAAMG,EAAE,GACN,gGADF,CAAA;EAGA,IAAMC,KAAK,GAAG,kBAAd,CAAA;AACA,EAAA,IAAMC,EAAE,GAAG,CAERD,YAAAA,GAAAA,KAFQ,gBAEQA,KAFR,GAAA,kFAAA,GAGRA,KAHQ,GAAA,UAAA,GAGQD,EAHR,GAGeC,IAAAA,GAAAA,KAHf,GAIRA,iHAAAA,GAAAA,KAJQ,iBAISD,EAJT,GAAA,OAAA,GAImBC,KAJnB,GAAA,6GAAA,GAKRA,KALQ,GAAA,cAAA,GAKYA,KALZ,GAAA,SAAA,GAK2BD,EAL3B,GAKqCC,OAAAA,GAAAA,KALrC,GAMRA,2FAAAA,GAAAA,KANQ,oBAMYA,KANZ,GAAA,SAAA,GAM2BD,EAN3B,GAAA,OAAA,GAMqCC,KANrC,GAORA,2FAAAA,GAAAA,KAPQ,GAOYA,cAAAA,GAAAA,KAPZ,GAO2BD,SAAAA,GAAAA,EAP3B,GAOqCC,OAAAA,GAAAA,KAPrC,iGAQRA,KARQ,GAAA,cAAA,GAQYA,KARZ,GAAA,SAAA,GAQ2BD,EAR3B,GAQqCC,OAAAA,GAAAA,KARrC,GASAA,mGAAAA,GAAAA,KATA,eASeD,EATf,GAAA,OAAA,GASyBC,KATzB,GAAA,oLAAA,EAYR1E,OAZQ,CAYA,cAZA,EAYgB,EAZhB,CAaRA,CAAAA,OAbQ,CAaA,KAbA,EAaO,EAbP,CAAA,CAcR4E,IAdQ,EAAX,CAfmB;;EAgCnB,IAAMC,QAAQ,GAAG,IAAIC,MAAJ,UAAkBL,EAAlB,GAAA,SAAA,GAA8BE,EAA9B,GAAjB,IAAA,CAAA,CAAA;AACA,EAAA,IAAMI,OAAO,GAAG,IAAID,MAAJ,CAAA,GAAA,GAAeL,EAAf,GAAhB,GAAA,CAAA,CAAA;AACA,EAAA,IAAMO,OAAO,GAAG,IAAIF,MAAJ,CAAA,GAAA,GAAeH,EAAf,GAAhB,GAAA,CAAA,CAAA;;AAEA,EAAA,IAAMM,EAAE,GAAG,SAALA,EAAK,CAAAhB,OAAO,EAAA;AAAA,IAAA,OAChBA,OAAO,IAAIA,OAAO,CAACiB,KAAnB,GACIL,QADJ,GAEI,IAAIC,MAAJ,SACQP,CAAC,CAACN,OAAD,CADT,GACqBQ,EADrB,GAC0BF,CAAC,CAACN,OAAD,CAD3B,GAAA,OAAA,GAC4CM,CAAC,CAACN,OAAD,CAD7C,GACyDU,EADzD,GAC8DJ,CAAC,CAC3DN,OAD2D,CAD/D,GAAA,GAAA,EAIE,GAJF,CAHY,CAAA;GAAlB,CAAA;;AAUAgB,EAAAA,EAAE,CAACR,EAAH,GAAQ,UAACR,OAAD,EAAA;IAAA,OACNA,OAAO,IAAIA,OAAO,CAACiB,KAAnB,GACIH,OADJ,GAEI,IAAID,MAAJ,CAAcP,EAAAA,GAAAA,CAAC,CAACN,OAAD,CAAf,GAA2BQ,EAA3B,GAAgCF,CAAC,CAACN,OAAD,CAAjC,EAA8C,GAA9C,CAHE,CAAA;GAAR,CAAA;;AAIAgB,EAAAA,EAAE,CAACN,EAAH,GAAQ,UAACV,OAAD,EAAA;IAAA,OACNA,OAAO,IAAIA,OAAO,CAACiB,KAAnB,GACIF,OADJ,GAEI,IAAIF,MAAJ,CAAcP,EAAAA,GAAAA,CAAC,CAACN,OAAD,CAAf,GAA2BU,EAA3B,GAAgCJ,CAAC,CAACN,OAAD,CAAjC,EAA8C,GAA9C,CAHE,CAAA;GAAR,CAAA;;AAKA,EAAA,IAAMkB,QAAQ,GAAd,oBAAA,CAAA;EACA,IAAMC,IAAI,GAAG,sBAAb,CAAA;AACA,EAAA,IAAMC,IAAI,GAAGJ,EAAE,CAACR,EAAH,GAAQrC,MAArB,CAAA;AACA,EAAA,IAAMkD,IAAI,GAAGL,EAAE,CAACN,EAAH,GAAQvC,MAArB,CAAA;EACA,IAAMmD,IAAI,GAAG,+DAAb,CAAA;EACA,IAAMC,MAAM,GACV,gEADF,CAAA;AAEA,EAAA,IAAMC,GAAG,GAAT,qCAAA,CAAA;EACA,IAAMC,IAAI,GAAG,gBAAb,CAAA;EACA,IAAMtC,IAAI,GAAG,oBAAb,CAAA;AACA,EAAA,IAAMuC,KAAK,GAASR,KAAAA,GAAAA,QAAT,gBAA4BC,IAA5B,GAAA,eAAA,GAAgDC,IAAhD,GAAwDC,GAAAA,GAAAA,IAAxD,GAAgEC,GAAAA,GAAAA,IAAhE,GAAuEC,MAAvE,GAAgFC,GAAhF,GAAuFC,GAAAA,GAAAA,IAAvF,GAA8FtC,IAAzG,CAAA;AACAiB,EAAAA,MAAM,GAAG,IAAIS,MAAJ,UAAkBa,KAAlB,GAAA,IAAA,EAA6B,GAA7B,CAAT,CAAA;AACA,EAAA,OAAOtB,MAAP,CAAA;AACD,CApED;;ACCA;;AAEA,IAAMuB,SAAO,GAAG;AACd;AACAC,EAAAA,KAAK,EAAE,sOAFO;AAGd;AACA;AACA;AACA;AACAC,EAAAA,GAAG,EAAE,gCAAA;AAPS,CAAhB,CAAA;AAUA,IAAMC,KAAK,GAAG;EACZC,OADY,EAAA,SAAA,OAAA,CACJvF,KADI,EACU;AACpB,IAAA,OAAOsF,KAAK,CAACE,MAAN,CAAaxF,KAAb,CAAA,IAAuByF,QAAQ,CAACzF,KAAD,EAAQ,EAAR,CAAR,KAAwBA,KAAtD,CAAA;GAFU;AAAA,EAAA,OAAA,EAAA,SAAA,KAAA,CAINA,KAJM,EAIQ;AAClB,IAAA,OAAOsF,KAAK,CAACE,MAAN,CAAaxF,KAAb,CAAA,IAAuB,CAACsF,KAAK,CAACC,OAAN,CAAcvF,KAAd,CAA/B,CAAA;GALU;EAOZ0F,KAPY,EAAA,SAAA,KAAA,CAON1F,KAPM,EAOQ;AAClB,IAAA,OAAOE,KAAK,CAACC,OAAN,CAAcH,KAAd,CAAP,CAAA;GARU;EAUZ2F,MAVY,EAAA,SAAA,MAAA,CAUL3F,KAVK,EAUS;IACnB,IAAIA,KAAK,YAAYqE,MAArB,EAA6B;AAC3B,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;IACD,IAAI;AACF,MAAA,OAAO,CAAC,CAAC,IAAIA,MAAJ,CAAWrE,KAAX,CAAT,CAAA;KADF,CAEE,OAAOxB,CAAP,EAAU;AACV,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;GAlBS;EAoBZoH,IApBY,EAAA,SAAA,IAAA,CAoBP5F,KApBO,EAoBO;AACjB,IAAA,OACE,OAAOA,KAAK,CAAC6F,OAAb,KAAyB,UAAzB,IACA,OAAO7F,KAAK,CAAC8F,QAAb,KAA0B,UAD1B,IAEA,OAAO9F,KAAK,CAAC+F,OAAb,KAAyB,UAFzB,IAGA,CAACC,KAAK,CAAChG,KAAK,CAAC6F,OAAN,EAAD,CAJR,CAAA;GArBU;EA4BZL,MA5BY,EAAA,SAAA,MAAA,CA4BLxF,KA5BK,EA4BS;AACnB,IAAA,IAAIgG,KAAK,CAAChG,KAAD,CAAT,EAAkB;AAChB,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;IACD,OAAO,OAAOA,KAAP,KAAiB,QAAxB,CAAA;GAhCU;EAkCZiG,MAlCY,EAAA,SAAA,MAAA,CAkCLjG,KAlCK,EAkCS;IACnB,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACsF,KAAK,CAACI,KAAN,CAAY1F,KAAZ,CAArC,CAAA;GAnCU;EAqCZkG,MArCY,EAAA,SAAA,MAAA,CAqCLlG,KArCK,EAqCS;IACnB,OAAO,OAAOA,KAAP,KAAiB,UAAxB,CAAA;GAtCU;EAwCZoF,KAxCY,EAAA,SAAA,KAAA,CAwCNpF,KAxCM,EAwCQ;IAClB,OACE,OAAOA,KAAP,KAAiB,QAAjB,IACAA,KAAK,CAACtB,MAAN,IAAgB,GADhB,IAEA,CAAC,CAACsB,KAAK,CAACmG,KAAN,CAAYhB,SAAO,CAACC,KAApB,CAHJ,CAAA;GAzCU;EA+CZgB,GA/CY,EAAA,SAAA,GAAA,CA+CRpG,KA/CQ,EA+CM;AAChB,IAAA,OACE,OAAOA,KAAP,KAAiB,QAAjB,IACAA,KAAK,CAACtB,MAAN,IAAgB,IADhB,IAEA,CAAC,CAACsB,KAAK,CAACmG,KAAN,CAAYE,WAAW,EAAvB,CAHJ,CAAA;GAhDU;EAsDZhB,GAtDY,EAAA,SAAA,GAAA,CAsDRrF,KAtDQ,EAsDM;AAChB,IAAA,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC,CAACA,KAAK,CAACmG,KAAN,CAAYhB,SAAO,CAACE,GAApB,CAAtC,CAAA;AACD,GAAA;AAxDW,CAAd,CAAA;;AA2DA,IAAMnH,MAAiB,GAAG,SAApBA,IAAoB,CAAC4E,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAA0C;AAClE,EAAA,IAAIV,IAAI,CAACS,QAAL,IAAiBvD,KAAK,KAAKC,SAA/B,EAA0C;IACxCsD,UAAQ,CAACT,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,CAAR,CAAA;AACA,IAAA,OAAA;AACD,GAAA;;EACD,IAAM8C,MAAM,GAAG,CACb,SADa,EAEb,OAFa,EAGb,OAHa,EAIb,QAJa,EAKb,QALa,EAMb,QANa,EAOb,OAPa,EAQb,QARa,EASb,MATa,EAUb,KAVa,EAWb,KAXa,CAAf,CAAA;AAaA,EAAA,IAAMC,QAAQ,GAAGzD,IAAI,CAAC5E,IAAtB,CAAA;;EACA,IAAIoI,MAAM,CAAChE,OAAP,CAAeiE,QAAf,CAA2B,GAAA,CAAC,CAAhC,EAAmC;IACjC,IAAI,CAACjB,KAAK,CAACiB,QAAD,CAAL,CAAgBvG,KAAhB,CAAL,EAA6B;MAC3B7B,MAAM,CAACY,IAAP,CACEC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiB6B,KAAjB,CAAuBiB,QAAvB,CAAD,EAAmCzD,IAAI,CAACI,SAAxC,EAAmDJ,IAAI,CAAC5E,IAAxD,CADR,CAAA,CAAA;AAGD,KALgC;;GAAnC,MAOO,IAAIqI,QAAQ,IAAI,OAAOvG,KAAP,KAAiB8C,IAAI,CAAC5E,IAAtC,EAA4C;IACjDC,MAAM,CAACY,IAAP,CACEC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiB6B,KAAjB,CAAuBiB,QAAvB,CAAD,EAAmCzD,IAAI,CAACI,SAAxC,EAAmDJ,IAAI,CAAC5E,IAAxD,CADR,CAAA,CAAA;AAGD,GAAA;AACF,CA/BD;;ACxEA,IAAMsI,KAAkB,GAAG,SAArBA,KAAqB,CAAC1D,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAA0C;AACnE,EAAA,IAAMpE,GAAG,GAAG,OAAO0D,IAAI,CAAC1D,GAAZ,KAAoB,QAAhC,CAAA;AACA,EAAA,IAAMqH,GAAG,GAAG,OAAO3D,IAAI,CAAC2D,GAAZ,KAAoB,QAAhC,CAAA;EACA,IAAMC,GAAG,GAAG,OAAO5D,IAAI,CAAC4D,GAAZ,KAAoB,QAAhC,CAHmE;;EAKnE,IAAMC,QAAQ,GAAG,iCAAjB,CAAA;EACA,IAAIC,GAAG,GAAG5G,KAAV,CAAA;EACA,IAAIqC,GAAG,GAAG,IAAV,CAAA;AACA,EAAA,IAAMwE,GAAG,GAAG,OAAO7G,KAAP,KAAiB,QAA7B,CAAA;AACA,EAAA,IAAMV,GAAG,GAAG,OAAOU,KAAP,KAAiB,QAA7B,CAAA;AACA,EAAA,IAAMK,GAAG,GAAGH,KAAK,CAACC,OAAN,CAAcH,KAAd,CAAZ,CAAA;;AACA,EAAA,IAAI6G,GAAJ,EAAS;AACPxE,IAAAA,GAAG,GAAG,QAAN,CAAA;GADF,MAEO,IAAI/C,GAAJ,EAAS;AACd+C,IAAAA,GAAG,GAAG,QAAN,CAAA;GADK,MAEA,IAAIhC,GAAJ,EAAS;AACdgC,IAAAA,GAAG,GAAG,OAAN,CAAA;AACD,GAjBkE;AAmBnE;AACA;;;EACA,IAAI,CAACA,GAAL,EAAU;AACR,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AACD,EAAA,IAAIhC,GAAJ,EAAS;IACPuG,GAAG,GAAG5G,KAAK,CAACtB,MAAZ,CAAA;AACD,GAAA;;AACD,EAAA,IAAIY,GAAJ,EAAS;AACP;IACAsH,GAAG,GAAG5G,KAAK,CAACT,OAAN,CAAcoH,QAAd,EAAwB,GAAxB,CAAA,CAA6BjI,MAAnC,CAAA;AACD,GAAA;;AACD,EAAA,IAAIU,GAAJ,EAAS;AACP,IAAA,IAAIwH,GAAG,KAAK9D,IAAI,CAAC1D,GAAjB,EAAsB;MACpBjB,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBpB,GAAjB,EAAsBjD,GAAvB,EAA4B0D,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAAC1D,GAAjD,CAAlB,CAAA,CAAA;AACD,KAAA;AACF,GAJD,MAIO,IAAIqH,GAAG,IAAI,CAACC,GAAR,IAAeE,GAAG,GAAG9D,IAAI,CAAC2D,GAA9B,EAAmC;IACxCtI,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBpB,GAAjB,EAAsBoE,GAAvB,EAA4B3D,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAAC2D,GAAjD,CAAlB,CAAA,CAAA;AACD,GAFM,MAEA,IAAIC,GAAG,IAAI,CAACD,GAAR,IAAeG,GAAG,GAAG9D,IAAI,CAAC4D,GAA9B,EAAmC;IACxCvI,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBpB,GAAjB,EAAsBqE,GAAvB,EAA4B5D,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAAC4D,GAAjD,CAAlB,CAAA,CAAA;AACD,GAFM,MAEA,IAAID,GAAG,IAAIC,GAAP,KAAeE,GAAG,GAAG9D,IAAI,CAAC2D,GAAX,IAAkBG,GAAG,GAAG9D,IAAI,CAAC4D,GAA5C,CAAJ,EAAsD;IAC3DvI,MAAM,CAACY,IAAP,CACEC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBpB,GAAjB,CAAsBmE,CAAAA,KAAvB,EAA8B1D,IAAI,CAACI,SAAnC,EAA8CJ,IAAI,CAAC2D,GAAnD,EAAwD3D,IAAI,CAAC4D,GAA7D,CADR,CAAA,CAAA;AAGD,GAAA;AACF,CA5CD;;ACAA,IAAMI,MAAI,GAAG,MAAb,CAAA;;AAEA,IAAMC,YAAuB,GAAG,SAA1BA,UAA0B,CAACjE,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAA0C;AACxEV,EAAAA,IAAI,CAACgE,MAAD,CAAJ,GAAa5G,KAAK,CAACC,OAAN,CAAc2C,IAAI,CAACgE,MAAD,CAAlB,CAA4BhE,GAAAA,IAAI,CAACgE,MAAD,CAAhC,GAAyC,EAAtD,CAAA;;EACA,IAAIhE,IAAI,CAACgE,MAAD,CAAJ,CAAWxE,OAAX,CAAmBtC,KAAnB,CAAA,KAA8B,CAAC,CAAnC,EAAsC;IACpC7B,MAAM,CAACY,IAAP,CACEC,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBqD,MAAjB,CAAD,EAAyBhE,IAAI,CAACI,SAA9B,EAAyCJ,IAAI,CAACgE,MAAD,CAAJ,CAAWE,IAAX,CAAgB,IAAhB,CAAzC,CADR,CAAA,CAAA;AAGD,GAAA;AACF,CAPD;;ACFA,IAAM7B,SAAoB,GAAG,SAAvBA,OAAuB,CAACrC,IAAD,EAAO9C,KAAP,EAAc2B,MAAd,EAAsBxD,MAAtB,EAA8BqF,OAA9B,EAA0C;EACrE,IAAIV,IAAI,CAACqC,OAAT,EAAkB;AAChB,IAAA,IAAIrC,IAAI,CAACqC,OAAL,YAAwBd,MAA5B,EAAoC;AAClC;AACA;AACA;AACAvB,MAAAA,IAAI,CAACqC,OAAL,CAAa8B,SAAb,GAAyB,CAAzB,CAAA;;MACA,IAAI,CAACnE,IAAI,CAACqC,OAAL,CAAaxB,IAAb,CAAkB3D,KAAlB,CAAL,EAA+B;QAC7B7B,MAAM,CAACY,IAAP,CACEC,MAAM,CACJwE,OAAO,CAACC,QAAR,CAAiB0B,OAAjB,CAAyB+B,QADrB,EAEJpE,IAAI,CAACI,SAFD,EAGJlD,KAHI,EAIJ8C,IAAI,CAACqC,OAJD,CADR,CAAA,CAAA;AAQD,OAAA;KAdH,MAeO,IAAI,OAAOrC,IAAI,CAACqC,OAAZ,KAAwB,QAA5B,EAAsC;MAC3C,IAAMgC,QAAQ,GAAG,IAAI9C,MAAJ,CAAWvB,IAAI,CAACqC,OAAhB,CAAjB,CAAA;;AACA,MAAA,IAAI,CAACgC,QAAQ,CAACxD,IAAT,CAAc3D,KAAd,CAAL,EAA2B;QACzB7B,MAAM,CAACY,IAAP,CACEC,MAAM,CACJwE,OAAO,CAACC,QAAR,CAAiB0B,OAAjB,CAAyB+B,QADrB,EAEJpE,IAAI,CAACI,SAFD,EAGJlD,KAHI,EAIJ8C,IAAI,CAACqC,OAJD,CADR,CAAA,CAAA;AAQD,OAAA;AACF,KAAA;AACF,GAAA;AACF,CA/BD;;ACIA,YAAe;AACb5B,EAAAA,QAAQ,EAARA,UADa;AAEbG,EAAAA,UAAU,EAAVA,UAFa;AAGbxF,EAAAA,IAAI,EAAJA,MAHa;AAIbsI,EAAAA,KAAK,EAALA,KAJa;AAKb,EAAA,MAAA,EAAMY,YALO;AAMbjC,EAAAA,OAAO,EAAPA,SAAAA;AANa,CAAf;;ACHA,IAAMkC,MAAwB,GAAG,SAA3BA,MAA2B,CAACvE,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC3E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAZ,IAAiC,CAAC8C,IAAI,CAACS,QAA3C,EAAqD;AACnD,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;AACDgH,IAAAA,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,EAAqD,QAArD,CAAA,CAAA;;AACA,IAAA,IAAI,CAACzD,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAjB,EAAoC;MAClCuH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;MACA+D,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;MACA+D,KAAK,CAACpC,OAAN,CAAcrC,IAAd,EAAoB9C,KAApB,EAA2B2B,MAA3B,EAAmCxD,MAAnC,EAA2CqF,OAA3C,CAAA,CAAA;;AACA,MAAA,IAAIV,IAAI,CAACY,UAAL,KAAoB,IAAxB,EAA8B;QAC5B6D,KAAK,CAAC7D,UAAN,CAAiBZ,IAAjB,EAAuB9C,KAAvB,EAA8B2B,MAA9B,EAAsCxD,MAAtC,EAA8CqF,OAA9C,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAnBD;;ACAA,IAAM+H,MAAwB,GAAG,SAA3BA,MAA2B,CAACpD,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC3E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAdD;;ACAA,IAAMqH,MAAwB,GAAG,SAA3BA,MAA2B,CAAC1C,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC3E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAItH,KAAK,KAAK,EAAd,EAAkB;AAChBA,MAAAA,KAAK,GAAGC,SAAR,CAAA;AACD,KAAA;;IACD,IAAIF,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;MACA+D,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAlBD;;ACAA,IAAMqJ,QAAyB,GAAG,SAA5BA,QAA4B,CAAC1E,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC5E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAdD;;ACAA,IAAMwH,MAAwB,GAAG,SAA3BA,MAA2B,CAAC7C,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC3E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;AACA,IAAA,IAAI,CAACzD,YAAY,CAACC,KAAD,CAAjB,EAA0B;MACxBuH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAdD;;ACAA,IAAMoH,OAAyB,GAAG,SAA5BA,OAA4B,CAACzC,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC5E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;MACA+D,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAfD;;ACAA,IAAMsJ,OAAyB,GAAG,SAA5BA,OAA4B,CAAC3E,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC5E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;MACA+D,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAfD;;ACDA,IAAMuH,KAAuB,GAAG,SAA1BA,KAA0B,CAAC5C,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC1E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;AACZ,IAAA,IAAI,CAACtH,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAAlC,KAA2C,CAAC8C,IAAI,CAACS,QAArD,EAA+D;AAC7D,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;AACDgH,IAAAA,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,EAAqD,OAArD,CAAA,CAAA;;AACA,IAAA,IAAIxD,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAArC,EAA2C;MACzCuH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;MACA+D,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAfD;;ACCA,IAAM8H,MAAwB,GAAG,SAA3BA,MAA2B,CAACnD,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC3E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;MACvBsH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAdD;;ACAA,IAAM2I,IAAI,GAAG,MAAb,CAAA;;AAEA,IAAMC,UAA4B,GAAG,SAA/BA,UAA+B,CACnCjE,IADmC,EAEnC9C,KAFmC,EAGnCO,QAHmC,EAInCoB,MAJmC,EAKnC6B,OALmC,EAMhC;EACH,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;IACA,IAAIxD,KAAK,KAAKC,SAAd,EAAyB;AACvBsH,MAAAA,KAAK,CAACT,IAAD,CAAL,CAAYhE,IAAZ,EAAkB9C,KAAlB,EAAyB2B,MAAzB,EAAiCxD,MAAjC,EAAyCqF,OAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CApBD;;ACFA,IAAMgH,OAAyB,GAAG,SAA5BA,OAA4B,CAACrC,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC5E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAZ,IAAiC,CAAC8C,IAAI,CAACS,QAA3C,EAAqD;AACnD,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;AACA,IAAA,IAAI,CAACzD,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAjB,EAAoC;MAClCuH,KAAK,CAACpC,OAAN,CAAcrC,IAAd,EAAoB9C,KAApB,EAA2B2B,MAA3B,EAAmCxD,MAAnC,EAA2CqF,OAA3C,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAdD;;ACAA,IAAMyH,IAAsB,GAAG,SAAzBA,IAAyB,CAAC9C,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;AACzE;EACA,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;EACA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAHyE;;AAMzE,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,EAAQ,MAAR,CAAZ,IAA+B,CAAC8C,IAAI,CAACS,QAAzC,EAAmD;AACjD,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;;AACA,IAAA,IAAI,CAACzD,YAAY,CAACC,KAAD,EAAQ,MAAR,CAAjB,EAAkC;AAChC,MAAA,IAAI0H,UAAJ,CAAA;;MAEA,IAAI1H,KAAK,YAAY2H,IAArB,EAA2B;AACzBD,QAAAA,UAAU,GAAG1H,KAAb,CAAA;AACD,OAFD,MAEO;AACL0H,QAAAA,UAAU,GAAG,IAAIC,IAAJ,CAAS3H,KAAT,CAAb,CAAA;AACD,OAAA;;MAEDuH,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB4E,UAAjB,EAA6B/F,MAA7B,EAAqCxD,MAArC,EAA6CqF,OAA7C,CAAA,CAAA;;AACA,MAAA,IAAIkE,UAAJ,EAAgB;AACdH,QAAAA,KAAK,CAACf,KAAN,CAAY1D,IAAZ,EAAkB4E,UAAU,CAAC7B,OAAX,EAAlB,EAAwClE,MAAxC,EAAgDxD,MAAhD,EAAwDqF,OAAxD,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CA3BD;;ACDA,IAAMoF,QAA0B,GAAG,SAA7BA,QAA6B,CAACT,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EAC7E,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;EACA,IAAMD,IAAI,GAAGgC,KAAK,CAACC,OAAN,CAAcH,KAAd,CAAuB,GAAA,OAAvB,GAAiC,OAAOA,KAArD,CAAA;AACAuH,EAAAA,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,EAAqDtF,IAArD,CAAA,CAAA;EACAqC,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CALD;;ACCA,IAAMD,IAAsB,GAAG,SAAzBA,IAAyB,CAAC4E,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;AACzE,EAAA,IAAM+C,QAAQ,GAAGzD,IAAI,CAAC5E,IAAtB,CAAA;EACA,IAAMC,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,EAAQuG,QAAR,CAAZ,IAAiC,CAACzD,IAAI,CAACS,QAA3C,EAAqD;AACnD,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;AACDgH,IAAAA,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,EAAqD+C,QAArD,CAAA,CAAA;;AACA,IAAA,IAAI,CAACxG,YAAY,CAACC,KAAD,EAAQuG,QAAR,CAAjB,EAAoC;MAClCgB,KAAK,CAACrJ,IAAN,CAAW4E,IAAX,EAAiB9C,KAAjB,EAAwB2B,MAAxB,EAAgCxD,MAAhC,EAAwCqF,OAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAfD;;ACAA,IAAMyJ,GAAqB,GAAG,SAAxBA,GAAwB,CAAC9E,IAAD,EAAO9C,KAAP,EAAcO,QAAd,EAAwBoB,MAAxB,EAAgC6B,OAAhC,EAA4C;EACxE,IAAMrF,MAAgB,GAAG,EAAzB,CAAA;AACA,EAAA,IAAMmJ,QAAQ,GACZxE,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkB5B,MAAM,CAAC2B,cAAP,CAAsBR,IAAI,CAAChE,KAA3B,CADtC,CAAA;;AAEA,EAAA,IAAIwI,QAAJ,EAAc;IACZ,IAAIvH,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAAC8C,IAAI,CAACS,QAAjC,EAA2C;AACzC,MAAA,OAAOhD,QAAQ,EAAf,CAAA;AACD,KAAA;;IACDgH,KAAK,CAAChE,QAAN,CAAeT,IAAf,EAAqB9C,KAArB,EAA4B2B,MAA5B,EAAoCxD,MAApC,EAA4CqF,OAA5C,CAAA,CAAA;AACD,GAAA;;EACDjD,QAAQ,CAACpC,MAAD,CAAR,CAAA;AACD,CAXD;;ACYA,iBAAe;AACbkJ,EAAAA,MAAM,EAANA,MADa;AAEbnB,EAAAA,MAAM,EAANA,MAFa;AAGbV,EAAAA,MAAM,EAANA,MAHa;AAIb,EAAA,SAAA,EAAAgC,QAJa;AAKb7B,EAAAA,MAAM,EAANA,MALa;AAMbJ,EAAAA,OAAO,EAAPA,OANa;AAOb,EAAA,OAAA,EAAAsC,OAPa;AAQbnC,EAAAA,KAAK,EAALA,KARa;AASbO,EAAAA,MAAM,EAANA,MATa;AAUb,EAAA,MAAA,EAAM6B,UAVO;AAWb3C,EAAAA,OAAO,EAAPA,OAXa;AAYbS,EAAAA,IAAI,EAAJA,IAZa;AAabQ,EAAAA,GAAG,EAAElI,IAbQ;AAcbmH,EAAAA,GAAG,EAAEnH,IAdQ;AAebkH,EAAAA,KAAK,EAAElH,IAfM;AAgBbqF,EAAAA,QAAQ,EAARA,QAhBa;AAiBbqE,EAAAA,GAAG,EAAHA,GAAAA;AAjBa,CAAf;;ACdO,SAASG,WAAT,GAAiD;EACtD,OAAO;AACL,IAAA,SAAA,EAAS,8BADJ;AAELxE,IAAAA,QAAQ,EAAE,gBAFL;AAGL,IAAA,MAAA,EAAM,sBAHD;AAILG,IAAAA,UAAU,EAAE,oBAJP;AAKLkC,IAAAA,IAAI,EAAE;AACJ5G,MAAAA,MAAM,EAAE,qCADJ;AAEJgJ,MAAAA,KAAK,EAAE,6CAFH;AAGJC,MAAAA,OAAO,EAAE,uBAAA;KARN;AAUL3C,IAAAA,KAAK,EAAE;AACL+B,MAAAA,MAAM,EAAE,gBADH;AAELnB,MAAAA,MAAM,EAAE,2BAFH;AAGLR,MAAAA,KAAK,EAAE,iBAHF;AAILO,MAAAA,MAAM,EAAE,iBAJH;AAKLT,MAAAA,MAAM,EAAE,gBALH;AAMLI,MAAAA,IAAI,EAAE,gBAND;AAOL,MAAA,SAAA,EAAS,gBAPJ;AAQLL,MAAAA,OAAO,EAAE,iBARJ;AASL,MAAA,OAAA,EAAO,gBATF;AAULI,MAAAA,MAAM,EAAE,sBAVH;AAWLP,MAAAA,KAAK,EAAE,sBAXF;AAYLgB,MAAAA,GAAG,EAAE,sBAZA;AAaLf,MAAAA,GAAG,EAAE,sBAAA;KAvBF;AAyBLgC,IAAAA,MAAM,EAAE;AACNjI,MAAAA,GAAG,EAAE,kCADC;AAENqH,MAAAA,GAAG,EAAE,mCAFC;AAGNC,MAAAA,GAAG,EAAE,wCAHC;AAINF,MAAAA,KAAK,EAAE,yCAAA;KA7BJ;AA+BLhB,IAAAA,MAAM,EAAE;AACNpG,MAAAA,GAAG,EAAE,kBADC;AAENqH,MAAAA,GAAG,EAAE,2BAFC;AAGNC,MAAAA,GAAG,EAAE,8BAHC;AAINF,MAAAA,KAAK,EAAE,8BAAA;KAnCJ;AAqCLd,IAAAA,KAAK,EAAE;AACLtG,MAAAA,GAAG,EAAE,iCADA;AAELqH,MAAAA,GAAG,EAAE,qCAFA;AAGLC,MAAAA,GAAG,EAAE,wCAHA;AAILF,MAAAA,KAAK,EAAE,wCAAA;KAzCJ;AA2CLrB,IAAAA,OAAO,EAAE;AACP+B,MAAAA,QAAQ,EAAE,uCAAA;KA5CP;AA8CLgB,IAAAA,KA9CK,EA8CG,SAAA,KAAA,GAAA;AACN,MAAA,IAAMC,MAAM,GAAGxI,IAAI,CAACqI,KAAL,CAAWrI,IAAI,CAACC,SAAL,CAAe,IAAf,CAAX,CAAf,CAAA;AACAuI,MAAAA,MAAM,CAACD,KAAP,GAAe,IAAA,CAAKA,KAApB,CAAA;AACA,MAAA,OAAOC,MAAP,CAAA;AACD,KAAA;GAlDH,CAAA;AAoDD,CAAA;AAEM,IAAM1E,QAAQ,GAAGsE,WAAW,EAA5B;;AC5BP;AACA;AACA;AACA;AACA;AACA;;IACMK;AACJ;AAgBA;AAIA,EAAA,SAAA,MAAA,CAAYC,UAAZ,EAA+B;IAAA,IAH/Bd,CAAAA,KAG+B,GAHK,IAGL,CAAA;IAAA,IAF/Be,CAAAA,SAE+B,GAFOC,QAEP,CAAA;IAC7B,IAAKC,CAAAA,MAAL,CAAYH,UAAZ,CAAA,CAAA;AACD,GAAA;;;;SAEDG,SAAA,SAAOjB,MAAAA,CAAAA,KAAP,EAAqB;AAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;IACnB,IAAI,CAACA,KAAL,EAAY;AACV,MAAA,MAAM,IAAI/F,KAAJ,CAAU,yCAAV,CAAN,CAAA;AACD,KAAA;;IACD,IAAI,OAAO+F,KAAP,KAAiB,QAAjB,IAA6BrH,KAAK,CAACC,OAAN,CAAcoH,KAAd,CAAjC,EAAuD;AACrD,MAAA,MAAM,IAAI/F,KAAJ,CAAU,yBAAV,CAAN,CAAA;AACD,KAAA;;IACD,IAAK+F,CAAAA,KAAL,GAAa,EAAb,CAAA;IAEAnG,MAAM,CAACC,IAAP,CAAYkG,KAAZ,EAAmB3I,OAAnB,CAA2B,UAAA6J,IAAI,EAAI;AACjC,MAAA,IAAMC,IAAU,GAAGnB,KAAK,CAACkB,IAAD,CAAxB,CAAA;AACA,MAAA,KAAI,CAAClB,KAAL,CAAWkB,IAAX,IAAmBvI,KAAK,CAACC,OAAN,CAAcuI,IAAd,CAAsBA,GAAAA,IAAtB,GAA6B,CAACA,IAAD,CAAhD,CAAA;KAFF,CAAA,CAAA;;;SAMFjF,WAAA,SAASA,QAAAA,CAAAA,SAAT,EAAsC;AACpC,IAAA,IAAIA,SAAJ,EAAc;MACZ,IAAK6E,CAAAA,SAAL,GAAiBnF,SAAS,CAAC4E,WAAW,EAAZ,EAAgBtE,SAAhB,CAA1B,CAAA;AACD,KAAA;;AACD,IAAA,OAAO,KAAK6E,SAAZ,CAAA;;;AAWFhB,EAAAA,MAAAA,CAAAA,WAAA,SAASqB,QAAAA,CAAAA,OAAT,EAA0BC,CAA1B,EAAuCC,EAAvC,EAA4E;AAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;AAAA,IAAA,IAAlDD,CAAkD,KAAA,KAAA,CAAA,EAAA;AAAlDA,MAAAA,CAAkD,GAAzC,EAAyC,CAAA;AAAA,KAAA;;AAAA,IAAA,IAArCC,EAAqC,KAAA,KAAA,CAAA,EAAA;MAArCA,EAAqC,GAA3B,SAAM,EAAA,GAAA,EAAqB,CAAA;AAAA,KAAA;;IAC1E,IAAIlH,MAAc,GAAGgH,OAArB,CAAA;IACA,IAAInF,OAAuB,GAAGoF,CAA9B,CAAA;IACA,IAAIrI,QAA0B,GAAGsI,EAAjC,CAAA;;AACA,IAAA,IAAI,OAAOrF,OAAP,KAAmB,UAAvB,EAAmC;AACjCjD,MAAAA,QAAQ,GAAGiD,OAAX,CAAA;AACAA,MAAAA,OAAO,GAAG,EAAV,CAAA;AACD,KAAA;;AACD,IAAA,IAAI,CAAC,IAAA,CAAK+D,KAAN,IAAenG,MAAM,CAACC,IAAP,CAAY,IAAA,CAAKkG,KAAjB,CAAA,CAAwB7I,MAAxB,KAAmC,CAAtD,EAAyD;AACvD,MAAA,IAAI6B,QAAJ,EAAc;AACZA,QAAAA,QAAQ,CAAC,IAAD,EAAOoB,MAAP,CAAR,CAAA;AACD,OAAA;;AACD,MAAA,OAAOG,OAAO,CAACC,OAAR,CAAgBJ,MAAhB,CAAP,CAAA;AACD,KAAA;;IAED,SAASmH,QAAT,CAAkBtI,OAAlB,EAAgE;MAC9D,IAAIrC,MAAuB,GAAG,EAA9B,CAAA;MACA,IAAIQ,MAA2B,GAAG,EAAlC,CAAA;;MAEA,SAASoK,GAAT,CAAavK,CAAb,EAAiD;AAC/C,QAAA,IAAI0B,KAAK,CAACC,OAAN,CAAc3B,CAAd,CAAJ,EAAsB;AAAA,UAAA,IAAA,OAAA,CAAA;;AACpBL,UAAAA,MAAM,GAAG,CAAAA,OAAAA,GAAAA,MAAM,EAAC6K,MAAP,CAAA,KAAA,CAAA,OAAA,EAAiBxK,CAAjB,CAAT,CAAA;AACD,SAFD,MAEO;UACLL,MAAM,CAACY,IAAP,CAAYP,CAAZ,CAAA,CAAA;AACD,SAAA;AACF,OAAA;;AAED,MAAA,KAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqB,OAAO,CAAC9B,MAA5B,EAAoCS,CAAC,EAArC,EAAyC;AACvC4J,QAAAA,GAAG,CAACvI,OAAO,CAACrB,CAAD,CAAR,CAAH,CAAA;AACD,OAAA;;AACD,MAAA,IAAI,CAAChB,MAAM,CAACO,MAAZ,EAAoB;AAClB6B,QAAAA,QAAQ,CAAC,IAAD,EAAOoB,MAAP,CAAR,CAAA;AACD,OAFD,MAEO;AACLhD,QAAAA,MAAM,GAAGF,kBAAkB,CAACN,MAAD,CAA3B,CAAA;QACCoC,QAAD,CAGWpC,MAHX,EAGmBQ,MAHnB,CAAA,CAAA;AAID,OAAA;AACF,KAAA;;IAED,IAAI6E,OAAO,CAACC,QAAZ,EAAsB;AACpB,MAAA,IAAIA,UAAQ,GAAG,IAAKA,CAAAA,QAAL,EAAf,CAAA;;MACA,IAAIA,UAAQ,KAAK8E,QAAjB,EAAkC;QAChC9E,UAAQ,GAAGsE,WAAW,EAAtB,CAAA;AACD,OAAA;;AACD5E,MAAAA,SAAS,CAACM,UAAD,EAAWD,OAAO,CAACC,QAAnB,CAAT,CAAA;MACAD,OAAO,CAACC,QAAR,GAAmBA,UAAnB,CAAA;AACD,KAPD,MAOO;AACLD,MAAAA,OAAO,CAACC,QAAR,GAAmB,IAAA,CAAKA,QAAL,EAAnB,CAAA;AACD,KAAA;;IAED,IAAMwF,MAA0C,GAAG,EAAnD,CAAA;AACA,IAAA,IAAM5H,IAAI,GAAGmC,OAAO,CAACnC,IAAR,IAAgBD,MAAM,CAACC,IAAP,CAAY,IAAKkG,CAAAA,KAAjB,CAA7B,CAAA;AACAlG,IAAAA,IAAI,CAACzC,OAAL,CAAa,UAAAsK,CAAC,EAAI;AAChB,MAAA,IAAM7I,GAAG,GAAG,MAAI,CAACkH,KAAL,CAAW2B,CAAX,CAAZ,CAAA;AACA,MAAA,IAAIlJ,KAAK,GAAG2B,MAAM,CAACuH,CAAD,CAAlB,CAAA;AACA7I,MAAAA,GAAG,CAACzB,OAAJ,CAAY,UAAAuK,CAAC,EAAI;QACf,IAAIrG,IAAsB,GAAGqG,CAA7B,CAAA;;AACA,QAAA,IAAI,OAAOrG,IAAI,CAACsG,SAAZ,KAA0B,UAA9B,EAA0C;UACxC,IAAIzH,MAAM,KAAKgH,OAAf,EAAwB;YACtBhH,MAAM,GAAA,QAAA,CAAA,EAAA,EAAQA,MAAR,CAAN,CAAA;AACD,WAAA;;UACD3B,KAAK,GAAG2B,MAAM,CAACuH,CAAD,CAAN,GAAYpG,IAAI,CAACsG,SAAL,CAAepJ,KAAf,CAApB,CAAA;AACD,SAAA;;AACD,QAAA,IAAI,OAAO8C,IAAP,KAAgB,UAApB,EAAgC;AAC9BA,UAAAA,IAAI,GAAG;AACLuG,YAAAA,SAAS,EAAEvG,IAAAA;WADb,CAAA;AAGD,SAJD,MAIO;UACLA,IAAI,GAAA,QAAA,CAAA,EAAA,EAAQA,IAAR,CAAJ,CAAA;AACD,SAdc;;;QAiBfA,IAAI,CAACuG,SAAL,GAAiB,MAAI,CAACC,mBAAL,CAAyBxG,IAAzB,CAAjB,CAAA;;AACA,QAAA,IAAI,CAACA,IAAI,CAACuG,SAAV,EAAqB;AACnB,UAAA,OAAA;AACD,SAAA;;QAEDvG,IAAI,CAAChE,KAAL,GAAaoK,CAAb,CAAA;AACApG,QAAAA,IAAI,CAACI,SAAL,GAAiBJ,IAAI,CAACI,SAAL,IAAkBgG,CAAnC,CAAA;QACApG,IAAI,CAAC5E,IAAL,GAAY,MAAI,CAACqL,OAAL,CAAazG,IAAb,CAAZ,CAAA;QACAmG,MAAM,CAACC,CAAD,CAAN,GAAYD,MAAM,CAACC,CAAD,CAAN,IAAa,EAAzB,CAAA;AACAD,QAAAA,MAAM,CAACC,CAAD,CAAN,CAAUnK,IAAV,CAAe;AACb+D,UAAAA,IAAI,EAAJA,IADa;AAEb9C,UAAAA,KAAK,EAALA,KAFa;AAGb2B,UAAAA,MAAM,EAANA,MAHa;AAIb7C,UAAAA,KAAK,EAAEoK,CAAAA;SAJT,CAAA,CAAA;OA1BF,CAAA,CAAA;KAHF,CAAA,CAAA;IAqCA,IAAMM,WAAW,GAAG,EAApB,CAAA;IACA,OAAO/H,QAAQ,CACbwH,MADa,EAEbzF,OAFa,EAGb,UAACiG,IAAD,EAAOC,IAAP,EAAgB;AACd,MAAA,IAAM5G,IAAI,GAAG2G,IAAI,CAAC3G,IAAlB,CAAA;AACA,MAAA,IAAI6G,IAAI,GACN,CAAC7G,IAAI,CAAC5E,IAAL,KAAc,QAAd,IAA0B4E,IAAI,CAAC5E,IAAL,KAAc,OAAzC,MACC,OAAO4E,IAAI,CAACnE,MAAZ,KAAuB,QAAvB,IACC,OAAOmE,IAAI,CAAC8G,YAAZ,KAA6B,QAF/B,CADF,CAAA;AAIAD,MAAAA,IAAI,GAAGA,IAAI,KAAK7G,IAAI,CAACS,QAAL,IAAkB,CAACT,IAAI,CAACS,QAAN,IAAkBkG,IAAI,CAACzJ,KAA9C,CAAX,CAAA;AACA8C,MAAAA,IAAI,CAAChE,KAAL,GAAa2K,IAAI,CAAC3K,KAAlB,CAAA;;AAEA,MAAA,SAAS+K,YAAT,CAAsBxH,GAAtB,EAAmCyH,MAAnC,EAAqD;AACnD,QAAA,OAAA,QAAA,CAAA,EAAA,EACKA,MADL,EAAA;AAEE5G,UAAAA,SAAS,EAAKJ,IAAI,CAACI,SAAV,SAAuBb,GAFlC;AAGEY,UAAAA,UAAU,EAAEH,IAAI,CAACG,UAAL,GAAsBH,EAAAA,CAAAA,MAAAA,CAAAA,IAAI,CAACG,UAA3B,EAAuCZ,CAAAA,GAAvC,CAA8C,CAAA,GAAA,CAACA,GAAD,CAAA;AAH5D,SAAA,CAAA,CAAA;AAKD,OAAA;;MAED,SAAS0H,EAAT,CAAYvL,CAAZ,EAAqD;AAAA,QAAA,IAAzCA,CAAyC,KAAA,KAAA,CAAA,EAAA;AAAzCA,UAAAA,CAAyC,GAAJ,EAAI,CAAA;AAAA,SAAA;;AACnD,QAAA,IAAIwL,SAAS,GAAG9J,KAAK,CAACC,OAAN,CAAc3B,CAAd,CAAA,GAAmBA,CAAnB,GAAuB,CAACA,CAAD,CAAvC,CAAA;;QACA,IAAI,CAACgF,OAAO,CAACyG,eAAT,IAA4BD,SAAS,CAACtL,MAA1C,EAAkD;AAChD0J,UAAAA,MAAM,CAACxK,OAAP,CAAe,kBAAf,EAAmCoM,SAAnC,CAAA,CAAA;AACD,SAAA;;QACD,IAAIA,SAAS,CAACtL,MAAV,IAAoBoE,IAAI,CAACL,OAAL,KAAiBxC,SAAzC,EAAoD;AAClD+J,UAAAA,SAAS,GAAG,EAAGhB,CAAAA,MAAH,CAAUlG,IAAI,CAACL,OAAf,CAAZ,CAAA;AACD,SAPkD;;;AAUnD,QAAA,IAAIyH,YAAY,GAAGF,SAAS,CAACG,GAAV,CAActH,eAAe,CAACC,IAAD,EAAOnB,MAAP,CAA7B,CAAnB,CAAA;;AAEA,QAAA,IAAI6B,OAAO,CAAC5B,KAAR,IAAiBsI,YAAY,CAACxL,MAAlC,EAA0C;AACxC8K,UAAAA,WAAW,CAAC1G,IAAI,CAAChE,KAAN,CAAX,GAA0B,CAA1B,CAAA;UACA,OAAO4K,IAAI,CAACQ,YAAD,CAAX,CAAA;AACD,SAAA;;QACD,IAAI,CAACP,IAAL,EAAW;UACTD,IAAI,CAACQ,YAAD,CAAJ,CAAA;AACD,SAFD,MAEO;AACL;AACA;AACA;UACA,IAAIpH,IAAI,CAACS,QAAL,IAAiB,CAACkG,IAAI,CAACzJ,KAA3B,EAAkC;AAChC,YAAA,IAAI8C,IAAI,CAACL,OAAL,KAAiBxC,SAArB,EAAgC;AAC9BiK,cAAAA,YAAY,GAAG,EACZlB,CAAAA,MADY,CACLlG,IAAI,CAACL,OADA,CAAA,CAEZ0H,GAFY,CAERtH,eAAe,CAACC,IAAD,EAAOnB,MAAP,CAFP,CAAf,CAAA;AAGD,aAJD,MAIO,IAAI6B,OAAO,CAAC3E,KAAZ,EAAmB;cACxBqL,YAAY,GAAG,CACb1G,OAAO,CAAC3E,KAAR,CACEiE,IADF,EAEE9D,MAAM,CAACwE,OAAO,CAACC,QAAR,CAAiBF,QAAlB,EAA4BT,IAAI,CAAChE,KAAjC,CAFR,CADa,CAAf,CAAA;AAMD,aAAA;;YACD,OAAO4K,IAAI,CAACQ,YAAD,CAAX,CAAA;AACD,WAAA;;UAED,IAAIE,YAAkC,GAAG,EAAzC,CAAA;;UACA,IAAItH,IAAI,CAAC8G,YAAT,EAAuB;YACrBxI,MAAM,CAACC,IAAP,CAAYoI,IAAI,CAACzJ,KAAjB,CAAA,CAAwBmK,GAAxB,CAA4B,UAAA9H,GAAG,EAAI;AACjC+H,cAAAA,YAAY,CAAC/H,GAAD,CAAZ,GAAoBS,IAAI,CAAC8G,YAAzB,CAAA;aADF,CAAA,CAAA;AAGD,WAAA;;UACDQ,YAAY,GAAA,QAAA,CAAA,EAAA,EACPA,YADO,EAEPX,IAAI,CAAC3G,IAAL,CAAUnE,MAFH,CAAZ,CAAA;UAKA,IAAM0L,iBAA6C,GAAG,EAAtD,CAAA;UAEAjJ,MAAM,CAACC,IAAP,CAAY+I,YAAZ,EAA0BxL,OAA1B,CAAkC,UAAAE,KAAK,EAAI;AACzC,YAAA,IAAMwL,WAAW,GAAGF,YAAY,CAACtL,KAAD,CAAhC,CAAA;AACA,YAAA,IAAMyL,eAAe,GAAGrK,KAAK,CAACC,OAAN,CAAcmK,WAAd,CAAA,GACpBA,WADoB,GAEpB,CAACA,WAAD,CAFJ,CAAA;AAGAD,YAAAA,iBAAiB,CAACvL,KAAD,CAAjB,GAA2ByL,eAAe,CAACJ,GAAhB,CACzBN,YAAY,CAACW,IAAb,CAAkB,IAAlB,EAAwB1L,KAAxB,CADyB,CAA3B,CAAA;WALF,CAAA,CAAA;AASA,UAAA,IAAMgL,MAAM,GAAG,IAAI1B,MAAJ,CAAWiC,iBAAX,CAAf,CAAA;AACAP,UAAAA,MAAM,CAACrG,QAAP,CAAgBD,OAAO,CAACC,QAAxB,CAAA,CAAA;;AACA,UAAA,IAAIgG,IAAI,CAAC3G,IAAL,CAAUU,OAAd,EAAuB;YACrBiG,IAAI,CAAC3G,IAAL,CAAUU,OAAV,CAAkBC,QAAlB,GAA6BD,OAAO,CAACC,QAArC,CAAA;YACAgG,IAAI,CAAC3G,IAAL,CAAUU,OAAV,CAAkB3E,KAAlB,GAA0B2E,OAAO,CAAC3E,KAAlC,CAAA;AACD,WAAA;;AACDiL,UAAAA,MAAM,CAACxC,QAAP,CAAgBmC,IAAI,CAACzJ,KAArB,EAA4ByJ,IAAI,CAAC3G,IAAL,CAAUU,OAAV,IAAqBA,OAAjD,EAA0D,UAAAiH,IAAI,EAAI;YAChE,IAAMC,WAAW,GAAG,EAApB,CAAA;;AACA,YAAA,IAAIR,YAAY,IAAIA,YAAY,CAACxL,MAAjC,EAAyC;AACvCgM,cAAAA,WAAW,CAAC3L,IAAZ,CAAA,KAAA,CAAA2L,WAAW,EAASR,YAAT,CAAX,CAAA;AACD,aAAA;;AACD,YAAA,IAAIO,IAAI,IAAIA,IAAI,CAAC/L,MAAjB,EAAyB;AACvBgM,cAAAA,WAAW,CAAC3L,IAAZ,CAAA,KAAA,CAAA2L,WAAW,EAASD,IAAT,CAAX,CAAA;AACD,aAAA;;YACDf,IAAI,CAACgB,WAAW,CAAChM,MAAZ,GAAqBgM,WAArB,GAAmC,IAApC,CAAJ,CAAA;WARF,CAAA,CAAA;AAUD,SAAA;AACF,OAAA;;AAED,MAAA,IAAIC,GAAJ,CAAA;;MACA,IAAI7H,IAAI,CAAC8H,cAAT,EAAyB;AACvBD,QAAAA,GAAG,GAAG7H,IAAI,CAAC8H,cAAL,CAAoB9H,IAApB,EAA0B2G,IAAI,CAACzJ,KAA/B,EAAsC+J,EAAtC,EAA0CN,IAAI,CAAC9H,MAA/C,EAAuD6B,OAAvD,CAAN,CAAA;AACD,OAFD,MAEO,IAAIV,IAAI,CAACuG,SAAT,EAAoB;QACzB,IAAI;AACFsB,UAAAA,GAAG,GAAG7H,IAAI,CAACuG,SAAL,CAAevG,IAAf,EAAqB2G,IAAI,CAACzJ,KAA1B,EAAiC+J,EAAjC,EAAqCN,IAAI,CAAC9H,MAA1C,EAAkD6B,OAAlD,CAAN,CAAA;SADF,CAEE,OAAO3E,KAAP,EAAc;UACdT,OAAO,CAACS,KAAR,IAAAT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAACS,KAAR,CAAgBA,KAAhB,CAAA,CADc;;AAGd,UAAA,IAAI,CAAC2E,OAAO,CAACqH,sBAAb,EAAqC;AACnCC,YAAAA,UAAU,CAAC,YAAM;AACf,cAAA,MAAMjM,KAAN,CAAA;aADQ,EAEP,CAFO,CAAV,CAAA;AAGD,WAAA;;AACDkL,UAAAA,EAAE,CAAClL,KAAK,CAAC4D,OAAP,CAAF,CAAA;AACD,SAAA;;QACD,IAAIkI,GAAG,KAAK,IAAZ,EAAkB;UAChBZ,EAAE,EAAA,CAAA;AACH,SAFD,MAEO,IAAIY,GAAG,KAAK,KAAZ,EAAmB;AACxBZ,UAAAA,EAAE,CACA,OAAOjH,IAAI,CAACL,OAAZ,KAAwB,UAAxB,GACIK,IAAI,CAACL,OAAL,CAAaK,IAAI,CAACI,SAAL,IAAkBJ,IAAI,CAAChE,KAApC,CADJ,GAEIgE,IAAI,CAACL,OAAL,IAAA,CAAmBK,IAAI,CAACI,SAAL,IAAkBJ,IAAI,CAAChE,KAA1C,YAHJ,CAAF,CAAA;AAKD,SANM,MAMA,IAAI6L,GAAG,YAAYzK,KAAnB,EAA0B;UAC/B6J,EAAE,CAACY,GAAD,CAAF,CAAA;AACD,SAFM,MAEA,IAAIA,GAAG,YAAYnJ,KAAnB,EAA0B;AAC/BuI,UAAAA,EAAE,CAACY,GAAG,CAAClI,OAAL,CAAF,CAAA;AACD,SAAA;AACF,OAAA;;AACD,MAAA,IAAIkI,GAAG,IAAKA,GAAD,CAAuBI,IAAlC,EAAwC;QACrCJ,GAAD,CAAuBI,IAAvB,CACE,YAAA;AAAA,UAAA,OAAMhB,EAAE,EAAR,CAAA;SADF,EAEE,UAAAvL,CAAC,EAAA;UAAA,OAAIuL,EAAE,CAACvL,CAAD,CAAN,CAAA;SAFH,CAAA,CAAA;AAID,OAAA;KAtIU,EAwIb,UAAAgC,OAAO,EAAI;MACTsI,QAAQ,CAACtI,OAAD,CAAR,CAAA;KAzIW,EA2IbmB,MA3Ia,CAAf,CAAA;;;SA+IF4H,UAAA,SAAQzG,OAAAA,CAAAA,IAAR,EAAgC;IAC9B,IAAIA,IAAI,CAAC5E,IAAL,KAAc+B,SAAd,IAA2B6C,IAAI,CAACqC,OAAL,YAAwBd,MAAvD,EAA+D;MAC7DvB,IAAI,CAAC5E,IAAL,GAAY,SAAZ,CAAA;AACD,KAAA;;IACD,IACE,OAAO4E,IAAI,CAACuG,SAAZ,KAA0B,UAA1B,IACAvG,IAAI,CAAC5E,IADL,IAEA,CAAC8M,UAAU,CAAC1H,cAAX,CAA0BR,IAAI,CAAC5E,IAA/B,CAHH,EAIE;MACA,MAAM,IAAIsD,KAAJ,CAAUxC,MAAM,CAAC,sBAAD,EAAyB8D,IAAI,CAAC5E,IAA9B,CAAhB,CAAN,CAAA;AACD,KAAA;;AACD,IAAA,OAAO4E,IAAI,CAAC5E,IAAL,IAAa,QAApB,CAAA;;;SAGFoL,sBAAA,SAAoBxG,mBAAAA,CAAAA,IAApB,EAA4C;AAC1C,IAAA,IAAI,OAAOA,IAAI,CAACuG,SAAZ,KAA0B,UAA9B,EAA0C;MACxC,OAAOvG,IAAI,CAACuG,SAAZ,CAAA;AACD,KAAA;;AACD,IAAA,IAAMhI,IAAI,GAAGD,MAAM,CAACC,IAAP,CAAYyB,IAAZ,CAAb,CAAA;AACA,IAAA,IAAMmI,YAAY,GAAG5J,IAAI,CAACiB,OAAL,CAAa,SAAb,CAArB,CAAA;;AACA,IAAA,IAAI2I,YAAY,KAAK,CAAC,CAAtB,EAAyB;AACvB5J,MAAAA,IAAI,CAAC6J,MAAL,CAAYD,YAAZ,EAA0B,CAA1B,CAAA,CAAA;AACD,KAAA;;AACD,IAAA,IAAI5J,IAAI,CAAC3C,MAAL,KAAgB,CAAhB,IAAqB2C,IAAI,CAAC,CAAD,CAAJ,KAAY,UAArC,EAAiD;MAC/C,OAAO2J,UAAU,CAACzH,QAAlB,CAAA;AACD,KAAA;;IACD,OAAOyH,UAAU,CAAC,IAAKzB,CAAAA,OAAL,CAAazG,IAAb,CAAD,CAAV,IAAkC7C,SAAzC,CAAA;;;;;;AA5TEmI,OAEG+C,WAAW,SAASA,QAAT,CAAkBjN,IAAlB,EAAgCmL,SAAhC,EAA2C;AAC3D,EAAA,IAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;AACnC,IAAA,MAAM,IAAI7H,KAAJ,CACJ,kEADI,CAAN,CAAA;AAGD,GAAA;;AACDwJ,EAAAA,UAAU,CAAC9M,IAAD,CAAV,GAAmBmL,SAAnB,CAAA;AACD;;AATGjB,OAWGxK,UAAUA;AAXbwK,OAaG3E,WAAW8E;AAbdH,OAeG4C,aAAaA;;;;"}