{"version": 3, "file": "select.mjs", "sources": ["../../../../../../packages/components/tree-select/src/select.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, nextTick, toRefs, watch } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElSelect from '@element-plus/components/select'\nimport { useNamespace } from '@element-plus/hooks'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { Ref } from 'vue'\nimport type ElTree from '@element-plus/components/tree'\n\nexport const useSelect = (\n  props,\n  { attrs, emit },\n  {\n    select,\n    tree,\n    key,\n  }: {\n    select: Ref<InstanceType<typeof ElSelect> | undefined>\n    tree: Ref<InstanceType<typeof ElTree> | undefined>\n    key: Ref<string>\n  }\n) => {\n  const ns = useNamespace('tree-select')\n\n  // update tree data when use filterMethod/remoteMethod\n  watch(\n    () => props.data,\n    () => {\n      if (props.filterable) {\n        nextTick(() => {\n          // let tree node expand only, same with tree filter\n          tree.value?.filter(select.value?.states.inputValue)\n        })\n      }\n    },\n    { flush: 'post' }\n  )\n\n  const result = {\n    ...pick(toRefs(props), Object.keys(ElSelect.props)),\n    ...attrs,\n    class: computed(() => attrs.class),\n    style: computed(() => attrs.style),\n    // attrs is not reactive, when v-model binding source changes,\n    // this listener is still old, see the bug(or test 'v-model source change'):\n    // https://github.com/element-plus/element-plus/issues/14204\n    'onUpdate:modelValue': (value) => emit(UPDATE_MODEL_EVENT, value),\n    valueKey: key,\n    popperClass: computed(() => {\n      const classes = [ns.e('popper')]\n      if (props.popperClass) classes.push(props.popperClass)\n      return classes.join(' ')\n    }),\n    filterMethod: (keyword = '') => {\n      if (props.filterMethod) {\n        props.filterMethod(keyword)\n      } else if (props.remoteMethod) {\n        props.remoteMethod(keyword)\n      } else {\n        // let tree node expand only, same with tree filter\n        tree.value?.filter(keyword)\n      }\n    },\n  }\n\n  return result\n}\n"], "names": [], "mappings": ";;;;;;AAKY,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AAClD,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,GAAG;AACL,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;AACzC,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;AAChC,IAAI,IAAI,KAAK,CAAC,UAAU,EAAE;AAC1B,MAAM,QAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AACnB,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACpH,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AACxB,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,GAAG,KAAK;AACZ,IAAI,KAAK,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC;AACtC,IAAI,KAAK,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC;AACtC,IAAI,qBAAqB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC;AACrE,IAAI,QAAQ,EAAE,GAAG;AACjB,IAAI,WAAW,EAAE,QAAQ,CAAC,MAAM;AAChC,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvC,MAAM,IAAI,KAAK,CAAC,WAAW;AAC3B,QAAQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACxC,MAAM,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,YAAY,EAAE,CAAC,OAAO,GAAG,EAAE,KAAK;AACpC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,KAAK,CAAC,YAAY,EAAE;AAC9B,QAAQ,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACpC,OAAO,MAAM,IAAI,KAAK,CAAC,YAAY,EAAE;AACrC,QAAQ,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACpC,OAAO,MAAM;AACb,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAChE,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}