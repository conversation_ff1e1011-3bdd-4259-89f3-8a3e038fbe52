# AI助手程序 - 报表OCR功能开发方案

## 一、项目概述

基于现有PMO系统的技术架构和千问大模型的实现经验，开发一个AI助手程序，首先实现报表OCR功能，后续扩展法规助手等功能。

### 1.1 前后端分离架构设计

```
                    前后端分离 + 多端统一架构

┌─────────────────────────────────────────────────────────────────┐
│                        客户端层（用户侧）                          │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│   Web应用       │   桌面客户端     │   移动应用       │   小程序    │
│  (Vue 3 + Vite) │  (Electron)     │  (Flutter)      │ (微信/支付宝) │
│  Element Plus   │  + Web技术栈    │  Dart + Flutter │  原生小程序  │
│  用户本地部署    │   用户本地安装   │   用户手机安装   │   即用即走   │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
                              │
                         HTTPS API 调用
                              │
┌─────────────────────────────────────────────────────────────────┐
│                      服务端层（公司侧）                           │
│                   统一后端API服务                                │
│                (FastAPI + Python 3.10)                        │
│                   部署在公司服务器                               │
└─────────────────────────────────────────────────────────────────┘
```

**前端特点（用户侧）：**
- 完全独立部署，可以是静态网站、桌面应用、移动应用等
- 通过HTTPS API与后端服务通信
- 支持离线缓存和本地存储
- 用户数据本地管理，隐私保护

**后端特点（公司侧）：**
- 部署在公司内网或云服务器
- 提供统一的RESTful API接口
- 集中管理AI模型和文档解析服务
- 统一的用户认证和权限管理
- 可扩展的微服务架构

**技术栈选择：**

**后端服务（公司服务器）：**
- FastAPI + Python 3.10（参考PMO系统架构）
- 文档解析：完全复用PMO-backend的document_parser.py
- AI模型：千问大模型（参考jintou_server实现）
- 数据库：MySQL + Redis缓存
- 文件存储：云存储或本地文件系统
- API网关：Nginx + SSL证书

**前端应用（用户侧）：**
- **Web应用**：Vue 3 + Vite + Element Plus，可部署为静态站点
- **桌面客户端**：Electron + Web技术栈，用户本地安装
- **移动应用**：Flutter + Dart，发布到应用商店
- **小程序**：微信/支付宝原生开发，即用即走

## 二、核心功能设计

### 2.1 报表OCR功能流程

```mermaid
graph TD
    A[用户上传批量文件] --> B[文件格式验证]
    B --> C[文档内容解析]
    C --> D[提取公司名称和时间]
    D --> E[去除隐私信息]
    E --> F[调用千问大模型]
    F --> G[生成标准化表格]
    G --> H[返回结果给用户]
```

### 2.2 文件解析能力

**支持的文件格式：**
- Excel文件：.xlsx, .xls
- Word文档：.docx, .doc
- PDF文件：.pdf
- PowerPoint：.pptx, .ppt
- 图片文件：.jpg, .jpeg, .png, .gif, .bmp
- 文本文件：.txt, .csv

**解析技术：**
- 完全复用PMO-backend的DocumentParser类
- 支持COM对象池优化（Excel/Word/PPT）
- MonkeyOCR用于PDF和图片识别
- 文档缓存机制提升性能

## 三、系统架构设计

### 3.1 前后端分离项目结构

```
ai-assistant-project/
├── backend-service/           # 后端服务（部署在公司服务器）
│   ├── app/
│   │   ├── api/               # RESTful API路由层
│   │   │   ├── v1/            # API版本控制
│   │   │   │   ├── auth.py           # 用户认证API
│   │   │   │   ├── report_ocr.py     # 报表OCR API
│   │   │   │   ├── legal_assistant.py # 法规助手API（后续）
│   │   │   │   └── health.py         # 健康检查API
│   │   │   └── middleware/    # 中间件
│   │   │       ├── cors.py           # 跨域处理
│   │   │       ├── auth.py           # 认证中间件
│   │   │       └── rate_limit.py     # 限流中间件
│   │   ├── services/          # 业务服务层
│   │   │   ├── document_parser.py    # 文档解析（复用PMO）
│   │   │   ├── company_extractor.py  # 公司信息提取
│   │   │   ├── privacy_cleaner.py    # 隐私信息清理
│   │   │   ├── qwen_service.py       # 千问大模型服务
│   │   │   └── table_generator.py    # 表格生成服务
│   │   ├── models/            # 数据模型
│   │   │   ├── database.py           # 数据库模型
│   │   │   ├── schemas.py            # API数据模式
│   │   │   └── enums.py              # 枚举定义
│   │   ├── core/              # 核心配置
│   │   │   ├── config.py             # 配置管理
│   │   │   ├── database.py           # 数据库连接
│   │   │   ├── security.py           # 安全配置
│   │   │   └── logging.py            # 日志配置
│   │   └── utils/             # 工具函数
│   │       ├── file_utils.py         # 文件处理工具
│   │       ├── text_utils.py         # 文本处理工具
│   │       └── validators.py         # 数据验证工具
│   ├── tests/                 # 测试代码
│   │   ├── test_api/
│   │   ├── test_services/
│   │   └── conftest.py
│   ├── deployment/            # 部署配置
│   │   ├── docker/
│   │   │   ├── Dockerfile
│   │   │   └── docker-compose.yml
│   │   ├── nginx/
│   │   │   └── nginx.conf
│   │   └── scripts/
│   │       ├── deploy.sh
│   │       └── backup.sh
│   ├── requirements.txt       # 依赖管理
│   ├── main.py               # 应用入口
│   └── README.md             # 后端文档
│
├── frontend-clients/          # 前端客户端（用户侧部署）
│   ├── web-client/           # Web客户端
│   │   ├── src/
│   │   │   ├── views/
│   │   │   │   ├── ReportOCR.vue     # 报表OCR页面
│   │   │   │   ├── LegalAssistant.vue # 法规助手页面（后续）
│   │   │   │   ├── Login.vue         # 登录页面
│   │   │   │   └── Dashboard.vue     # 仪表板页面
│   │   │   ├── components/    # 可复用组件
│   │   │   │   ├── FileUploader.vue  # 文件上传组件
│   │   │   │   ├── ResultTable.vue   # 结果表格组件
│   │   │   │   ├── ProgressBar.vue   # 进度条组件
│   │   │   │   └── Layout/           # 布局组件
│   │   │   │       ├── AppHeader.vue # 应用头部
│   │   │   │       ├── AppSidebar.vue # 应用侧边栏
│   │   │   │       └── AppFooter.vue # 应用底部
│   │   │   ├── api/
│   │   │   │   ├── client.js         # API客户端配置
│   │   │   │   ├── report.js         # 报表API调用
│   │   │   │   ├── auth.js           # 认证API调用
│   │   │   │   └── interceptors.js   # 请求拦截器
│   │   │   ├── router/
│   │   │   │   └── index.js          # 路由配置
│   │   │   ├── stores/        # Pinia状态管理
│   │   │   │   ├── auth.js           # 认证状态
│   │   │   │   ├── report.js         # 报表状态
│   │   │   │   └── app.js            # 应用状态
│   │   │   ├── utils/
│   │   │   │   ├── request.js        # HTTP请求封装
│   │   │   │   ├── storage.js        # 本地存储工具
│   │   │   │   └── validators.js     # 前端验证工具
│   │   │   └── assets/        # 静态资源
│   │   │       ├── styles/           # 样式文件
│   │   │       ├── images/           # 图片资源
│   │   │       └── icons/            # 图标资源
│   │   ├── public/
│   │   │   ├── favicon.ico
│   │   │   └── manifest.json
│   │   ├── dist/              # 构建输出（用户部署）
│   │   ├── package.json
│   │   ├── vite.config.js
│   │   ├── index.html
│   │   └── README.md          # Web客户端文档
│   │
│   ├── desktop-client/        # 桌面客户端（后续开发）
│   │   ├── src/
│   │   │   ├── main/          # Electron主进程
│   │   │   │   ├── main.js           # 主进程入口
│   │   │   │   ├── menu.js           # 应用菜单
│   │   │   │   └── updater.js        # 自动更新
│   │   │   ├── renderer/      # 渲染进程（复用web组件）
│   │   │   │   └── (复用web-client的src目录)
│   │   │   └── preload/       # 预加载脚本
│   │   │       └── preload.js
│   │   ├── build/             # 构建配置
│   │   │   ├── icons/         # 应用图标
│   │   │   └── installer.nsh  # 安装程序配置
│   │   ├── dist/              # 打包输出（用户安装）
│   │   ├── package.json
│   │   └── README.md          # 桌面客户端文档
│   │
│   ├── mobile-client/         # 移动客户端（后续开发）
│   │   ├── lib/
│   │   │   ├── main.dart      # Flutter入口
│   │   │   ├── pages/         # 页面
│   │   │   ├── widgets/       # 组件
│   │   │   ├── services/      # 服务层
│   │   │   └── models/        # 数据模型
│   │   ├── android/           # Android配置
│   │   ├── ios/               # iOS配置
│   │   ├── build/             # 构建输出（发布到应用商店）
│   │   ├── pubspec.yaml
│   │   └── README.md          # 移动客户端文档
│   │
│   └── mini-program/          # 小程序客户端（后续开发）
│       ├── pages/             # 小程序页面
│       ├── components/        # 小程序组件
│       ├── utils/             # 工具函数
│       ├── app.js
│       ├── app.json
│       └── README.md          # 小程序文档
│
├── shared-resources/          # 共享资源
│   ├── api-docs/              # API文档
│   │   ├── openapi.yaml       # OpenAPI规范
│   │   └── postman/           # Postman集合
│   ├── design-assets/         # 设计资源
│   │   ├── ui-mockups/        # UI设计稿
│   │   ├── icons/             # 图标库
│   │   └── brand/             # 品牌资源
│   ├── configs/               # 配置文件
│   │   ├── env-templates/     # 环境变量模板
│   │   └── deployment/        # 部署配置
│   └── docs/                  # 项目文档
│       ├── architecture.md    # 架构文档
│       ├── api-guide.md       # API使用指南
│       └── deployment-guide.md # 部署指南
│
└── README.md                  # 项目总览
```

### 3.2 前后端分离API设计

**API服务地址配置：**
```javascript
// 前端配置示例
const API_CONFIG = {
  // 开发环境 - 公司内网
  development: 'http://*************:8000',
  // 测试环境 - 公司测试服务器
  testing: 'https://api-test.company.com',
  // 生产环境 - 公司生产服务器
  production: 'https://api.company.com'
}
```

**核心API端点设计：**

```python
# ===== 认证相关API =====
POST /api/v1/auth/login
Content-Type: application/json
Body: {
    "username": "<EMAIL>",
    "password": "password123"
}
Response: {
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "Bearer",
        "expires_in": 3600,
        "user_info": {
            "id": 1,
            "username": "<EMAIL>",
            "name": "张三",
            "role": "user"
        }
    }
}

POST /api/v1/auth/refresh-token
POST /api/v1/auth/logout
GET /api/v1/auth/profile

# ===== 报表OCR API =====
POST /api/v1/report-ocr/upload-files
Content-Type: multipart/form-data
Headers: Authorization: Bearer {access_token}
Body: FormData with files
Response: {
    "code": 200,
    "message": "文件上传成功",
    "data": {
        "task_id": "uuid-task-id",
        "files_count": 3,
        "files": [
            {
                "file_id": "uuid-file-id",
                "filename": "财务报表.xlsx",
                "file_size": 1024000,
                "status": "parsed",
                "content": "解析后的内容...",
                "company_info": {
                    "main_company": "某某科技有限公司",
                    "companies": ["某某科技有限公司", "子公司A"],
                    "report_date": "2024-12-31",
                    "report_type": "年度财务报表"
                },
                "parsed_at": "2024-01-15T10:30:00Z"
            }
        ]
    }
}

POST /api/v1/report-ocr/generate-table
Content-Type: application/json
Headers: Authorization: Bearer {access_token}
Body: {
    "file_id": "uuid-file-id",
    "content": "清理后的报表内容",
    "company_name": "某某科技有限公司",
    "options": {
        "remove_privacy": true,
        "table_format": "standard"
    }
}
Response: {
    "code": 200,
    "message": "表格生成成功",
    "data": {
        "table_id": "uuid-table-id",
        "company_name": "某某科技有限公司",
        "report_date": "2024-12-31",
        "report_type": "年度财务报表",
        "key_metrics": [
            {
                "metric_name": "营业收入",
                "current_value": "*********0",
                "previous_value": "*********",
                "unit": "元",
                "change_rate": "25%"
            }
        ],
        "financial_data": [
            {
                "category": "资产",
                "items": [
                    {
                        "item_name": "货币资金",
                        "current_period": "*********",
                        "previous_period": "*********",
                        "change_amount": "*********",
                        "change_rate": "25%"
                    }
                ]
            }
        ],
        "generated_at": "2024-01-15T10:35:00Z"
    }
}

# ===== 任务管理API =====
GET /api/v1/tasks/{task_id}
Response: {
    "code": 200,
    "data": {
        "task_id": "uuid-task-id",
        "status": "processing|completed|failed",
        "progress": 75,
        "total_files": 10,
        "processed_files": 7,
        "current_file": "report3.pdf",
        "started_at": "2024-01-15T10:30:00Z",
        "estimated_completion": "2024-01-15T10:40:00Z"
    }
}

# ===== 历史记录API =====
GET /api/v1/history/reports?page=1&size=20&date_from=2024-01-01&date_to=2024-01-31
GET /api/v1/history/reports/{record_id}
DELETE /api/v1/history/reports/{record_id}
POST /api/v1/history/reports/{record_id}/export

# ===== 系统API =====
GET /api/v1/health
Response: {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0",
    "services": {
        "database": "healthy",
        "redis": "healthy",
        "qwen_api": "healthy"
    }
}

GET /api/v1/version
GET /api/v1/config/client
```

**API安全设计：**
- JWT Token认证
- HTTPS强制加密
- CORS跨域配置
- API限流和防护
- 请求签名验证（可选）

## 四、详细实现方案

### 4.1 文档解析服务（复用PMO实现）

```python
# app/services/document_parser.py
# 完全复用PMO-backend的DocumentParser类
from pmo_backend.app.services.document_parser import DocumentParser

class ReportDocumentParser(DocumentParser):
    """报表文档解析器，继承PMO的DocumentParser"""
    
    def __init__(self):
        super().__init__()
        # 可以添加报表特定的配置
        
    async def parse_report_files(self, files: List[UploadFile]) -> List[Dict]:
        """批量解析报表文件"""
        results = []
        
        for file in files:
            file_content = await file.read()
            
            # 使用父类的解析方法
            parsed_content = self.parse_document(
                file_content, 
                file.filename, 
                file.content_type
            )
            
            # 处理异步解析（PDF、图片等）
            if parsed_content.startswith("PDF_TO_PROCESS:"):
                pdf_base64 = parsed_content.replace("PDF_TO_PROCESS:", "")
                import base64
                pdf_bytes = base64.b64decode(pdf_base64)
                parsed_content = await self.parse_pdf_file(pdf_bytes)
            
            elif parsed_content.startswith("IMAGE_TO_PROCESS:"):
                image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                parsed_content = await self.parse_image_with_vision_model(image_base64)
            
            results.append({
                "filename": file.filename,
                "content": parsed_content,
                "file_type": file.content_type
            })
            
        return results
```

### 4.2 公司信息提取服务

```python
# app/services/company_extractor.py
import re
from typing import Dict, Optional

class CompanyExtractor:
    """公司信息提取器"""
    
    def __init__(self):
        # 公司名称匹配模式
        self.company_patterns = [
            r'([^，。\s]+(?:有限公司|股份有限公司|集团|公司|企业|机构))',
            r'([^，。\s]+(?:银行|保险|证券|基金|信托|租赁))',
            r'([^，。\s]+(?:投资|控股|资产|管理)(?:有限公司|公司)?)',
        ]
        
        # 时间匹配模式
        self.time_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})',
        ]
    
    def extract_company_info(self, content: str) -> Dict:
        """提取公司名称和时间信息"""
        companies = set()
        times = set()
        
        # 提取公司名称
        for pattern in self.company_patterns:
            matches = re.findall(pattern, content)
            companies.update(matches)
        
        # 提取时间信息
        for pattern in self.time_patterns:
            matches = re.findall(pattern, content)
            times.update(matches)
        
        return {
            "companies": list(companies),
            "times": list(times),
            "main_company": list(companies)[0] if companies else None,
            "report_date": list(times)[0] if times else None
        }
```

### 4.3 隐私信息清理服务

```python
# app/services/privacy_cleaner.py
import re
from typing import str

class PrivacyCleaner:
    """隐私信息清理器"""
    
    def __init__(self):
        # 敏感信息匹配模式
        self.sensitive_patterns = [
            (r'\d{15,18}', '[身份证号]'),  # 身份证号
            (r'\d{11}', '[手机号]'),       # 手机号
            (r'\d{3,4}-\d{7,8}', '[电话号码]'),  # 固定电话
            (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', '[邮箱地址]'),  # 邮箱
            (r'\d{16,19}', '[银行卡号]'),   # 银行卡号
        ]
        
        # 公司名称保留模式（不清理）
        self.preserve_patterns = [
            r'[^，。\s]+(?:有限公司|股份有限公司|集团|公司|企业|机构)',
            r'[^，。\s]+(?:银行|保险|证券|基金|信托|租赁)',
        ]
    
    def clean_privacy_info(self, content: str, company_name: str = None) -> str:
        """清理隐私信息，保留公司名称"""
        cleaned_content = content
        
        # 清理敏感信息
        for pattern, replacement in self.sensitive_patterns:
            cleaned_content = re.sub(pattern, replacement, cleaned_content)
        
        # 如果指定了公司名称，将其他公司名称也进行脱敏
        if company_name:
            # 保留主要公司名称，其他公司名称替换为[其他公司]
            for preserve_pattern in self.preserve_patterns:
                companies = re.findall(preserve_pattern, cleaned_content)
                for company in companies:
                    if company != company_name:
                        cleaned_content = cleaned_content.replace(company, '[其他公司]')
        
        return cleaned_content
```

### 4.4 千问大模型服务（复用jintou_server实现）

```python
# app/services/qwen_service.py
# 参考jintou_server的chat_api.py实现
import aiohttp
import json
import os
from typing import Dict, List

class QwenService:
    """千问大模型服务"""
    
    def __init__(self):
        # 使用与jintou_server相同的配置
        self.api_url = os.getenv("QWEN_API_URL", "http://**********:8001/v1/chat/completions")
        self.api_key = os.getenv("QWEN_API_KEY", "szjf@2025")
        self.model = os.getenv("QWEN_MODEL", "qwen-32b")
    
    async def generate_standard_table(self, report_content: str, company_name: str = None) -> Dict:
        """生成标准化表格"""
        
        # 构建提示词
        prompt = f"""
        请分析以下报表内容，提取关键信息并生成标准化表格。

        报表内容：
        {report_content}

        请按照以下格式生成JSON表格数据：
        {{
            "company_name": "公司名称",
            "report_date": "报表日期",
            "report_type": "报表类型",
            "key_metrics": [
                {{
                    "metric_name": "指标名称",
                    "metric_value": "指标值",
                    "unit": "单位",
                    "period": "期间"
                }}
            ],
            "financial_data": [
                {{
                    "item": "科目名称",
                    "current_period": "本期金额",
                    "previous_period": "上期金额",
                    "change_rate": "变动率"
                }}
            ],
            "summary": "报表摘要"
        }}

        要求：
        1. 准确提取数值和日期信息
        2. 保持原始数据的准确性
        3. 统一单位格式（万元、亿元等）
        4. 计算变动率和趋势
        """
        
        messages = [
            {"role": "system", "content": "你是一个专业的财务报表分析助手，擅长提取和标准化财务数据。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.api_url,
                    json={
                        "model": self.model,
                        "messages": messages,
                        "temperature": 0.1,  # 低温度确保准确性
                        "max_tokens": 4000
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.api_key}"
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        result_text = data["choices"][0]["message"]["content"]
                        
                        # 尝试解析JSON结果
                        try:
                            result_json = json.loads(result_text)
                            return {
                                "success": True,
                                "data": result_json
                            }
                        except json.JSONDecodeError:
                            # 如果不是JSON格式，返回原始文本
                            return {
                                "success": True,
                                "data": {"raw_result": result_text}
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"API调用失败: {response.status}"
                        }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"千问大模型调用异常: {str(e)}"
            }
```

## 五、Web应用开发方案（第一阶段）

### 5.1 Web应用技术栈

**核心技术：**
- Vue 3 + Composition API
- Vite 构建工具
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 路由管理
- Axios HTTP客户端

**开发特点：**
- 响应式设计，兼容桌面和移动端
- 组件化开发，为后续Electron复用做准备
- TypeScript支持（可选）
- PWA支持，提升用户体验

### 5.2 Web应用主页面设计

```vue
<!-- web-app/src/views/ReportOCR.vue -->
<template>
  <div class="report-ocr-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Document /></el-icon>
        报表OCR识别
      </h1>
      <p class="page-description">
        支持批量上传Excel、Word、PDF、PPT、图片等格式的报表文件，
        自动提取关键信息并生成标准化表格
      </p>
    </div>

    <!-- 功能区域 -->
    <div class="main-content">
      <!-- 文件上传区域 -->
      <el-card class="upload-section" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>文件上传</span>
            <el-tag v-if="uploadedFiles.length > 0" type="success">
              已选择 {{ uploadedFiles.length }} 个文件
            </el-tag>
          </div>
        </template>

        <FileUploader
          @files-selected="handleFilesSelected"
          @files-removed="handleFilesRemoved"
          :loading="uploading"
          :max-files="20"
          :max-size="10"
        />
      </el-card>

      <!-- 处理进度 -->
      <el-card v-if="processing" class="progress-section" shadow="hover">
        <template #header>
          <span>处理进度</span>
        </template>

        <ProgressBar
          :current="processedCount"
          :total="totalFiles"
          :status="processStatus"
          :current-file="currentProcessingFile"
        />
      </el-card>

      <!-- 结果展示 -->
      <el-card v-if="results.length > 0" class="results-section" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>处理结果</span>
            <div class="header-actions">
              <el-button
                type="primary"
                @click="exportAllResults"
                :loading="exporting"
              >
                <el-icon><Download /></el-icon>
                导出全部
              </el-button>
              <el-button @click="clearResults">
                <el-icon><Delete /></el-icon>
                清空结果
              </el-button>
            </div>
          </div>
        </template>

        <ResultTable
          :data="results"
          @download="handleDownload"
          @regenerate="handleRegenerate"
          @preview="handlePreview"
        />
      </el-card>
    </div>

    <!-- 历史记录抽屉 -->
    <el-drawer
      v-model="historyDrawerVisible"
      title="历史记录"
      size="60%"
    >
      <HistoryList
        @load-history="handleLoadHistory"
        @delete-history="handleDeleteHistory"
      />
    </el-drawer>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文件预览"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <FilePreview
        :file-data="previewFileData"
        :table-data="previewTableData"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Download, Delete } from '@element-plus/icons-vue'

// 组件引入
import FileUploader from '@/components/FileUploader.vue'
import ProgressBar from '@/components/ProgressBar.vue'
import ResultTable from '@/components/ResultTable.vue'
import HistoryList from '@/components/HistoryList.vue'
import FilePreview from '@/components/FilePreview.vue'

// API引入
import { uploadFiles, generateTable, exportResults } from '@/api/report.js'
import { useReportStore } from '@/stores/report.js'

// 状态管理
const reportStore = useReportStore()

// 响应式数据
const uploadedFiles = ref([])
const uploading = ref(false)
const processing = ref(false)
const processedCount = ref(0)
const totalFiles = ref(0)
const processStatus = ref('waiting')
const currentProcessingFile = ref('')
const results = ref([])
const exporting = ref(false)

// 抽屉和对话框状态
const historyDrawerVisible = ref(false)
const previewDialogVisible = ref(false)
const previewFileData = ref(null)
const previewTableData = ref(null)

// 处理文件选择
const handleFilesSelected = async (files) => {
  uploadedFiles.value = files

  if (files.length === 0) return

  uploading.value = true
  totalFiles.value = files.length
  processStatus.value = 'uploading'

  try {
    // 1. 上传并解析文件
    const uploadResult = await uploadFiles(files)

    if (uploadResult.code === 200) {
      uploading.value = false
      processing.value = true
      processStatus.value = 'processing'

      // 2. 逐个生成标准化表格
      const tableResults = []

      for (let i = 0; i < uploadResult.data.files.length; i++) {
        const file = uploadResult.data.files[i]
        currentProcessingFile.value = file.filename

        try {
          const tableResult = await generateTable({
            content: file.full_content,
            filename: file.filename,
            company_name: file.company_info?.main_company
          })

          tableResults.push({
            id: `${Date.now()}_${i}`,
            filename: file.filename,
            success: true,
            table_data: tableResult.data,
            original_content: file.content,
            company_info: file.company_info,
            processed_at: new Date().toLocaleString()
          })

        } catch (error) {
          tableResults.push({
            id: `${Date.now()}_${i}`,
            filename: file.filename,
            success: false,
            error: error.message,
            processed_at: new Date().toLocaleString()
          })
        }

        processedCount.value = i + 1
      }

      results.value = tableResults
      processing.value = false
      processStatus.value = 'completed'
      currentProcessingFile.value = ''

      // 保存到状态管理
      reportStore.addResults(tableResults)

      ElMessage.success(`成功处理 ${tableResults.filter(r => r.success).length} 个文件`)

    } else {
      throw new Error(uploadResult.message || '文件上传失败')
    }

  } catch (error) {
    ElMessage.error(`处理失败: ${error.message}`)
    processStatus.value = 'failed'
  } finally {
    uploading.value = false
    processing.value = false
  }
}

// 其他处理函数
const handleFilesRemoved = (files) => {
  uploadedFiles.value = files
}

const handleDownload = async (item) => {
  try {
    const blob = new Blob([JSON.stringify(item.table_data, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${item.filename}_result.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const handleRegenerate = async (item) => {
  try {
    const result = await generateTable({
      content: item.original_content,
      filename: item.filename,
      company_name: item.company_info?.main_company
    })

    // 更新结果
    const index = results.value.findIndex(r => r.id === item.id)
    if (index !== -1) {
      results.value[index] = {
        ...item,
        table_data: result.data,
        success: true,
        processed_at: new Date().toLocaleString()
      }
    }

    ElMessage.success('重新生成成功')
  } catch (error) {
    ElMessage.error('重新生成失败')
  }
}

const handlePreview = (item) => {
  previewFileData.value = item
  previewTableData.value = item.table_data
  previewDialogVisible.value = true
}

const exportAllResults = async () => {
  if (results.value.length === 0) {
    ElMessage.warning('没有可导出的结果')
    return
  }

  exporting.value = true

  try {
    const successResults = results.value.filter(r => r.success)
    await exportResults(successResults)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const clearResults = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有结果吗？', '确认操作', {
      type: 'warning'
    })

    results.value = []
    uploadedFiles.value = []
    processedCount.value = 0
    totalFiles.value = 0

    ElMessage.success('已清空结果')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  // 加载历史结果
  const historyResults = reportStore.getRecentResults()
  if (historyResults.length > 0) {
    results.value = historyResults
  }
})
</script>

<style scoped>
.report-ocr-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.page-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.upload-section,
.progress-section,
.results-section {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-ocr-container {
    padding: 10px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
```

## 六、前后端分离部署方案

### 6.1 后端服务部署（公司服务器）

**部署架构：**
```
                    公司服务器部署架构
┌─────────────────────────────────────────────────────────────┐
│                      负载均衡层                              │
│                   Nginx + SSL证书                          │
│                 (api.company.com)                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   应用服务层                                │
│              FastAPI + Gunicorn                           │
│                (多实例部署)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   数据存储层                                │
│         MySQL + Redis + 文件存储                          │
└─────────────────────────────────────────────────────────────┘
```

**Docker容器化部署：**
```yaml
# backend-service/deployment/docker/docker-compose.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs:/var/log/nginx
    depends_on:
      - api-server
    restart: unless-stopped

  api-server:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=ai_assistant
      - DB_USER=ai_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=redis://redis:6379
      - QWEN_API_URL=http://**********:8001/v1/chat/completions
      - QWEN_API_KEY=${QWEN_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CORS_ORIGINS=https://client.company.com,https://app.company.com
    volumes:
      - ./uploads:/app/uploads
      - ./cache:/app/cache
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2  # 多实例部署

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=ai_assistant
      - MYSQL_USER=ai_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  # 监控服务
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  grafana_data:

networks:
  default:
    driver: bridge
```

### 6.2 前端客户端部署（用户侧）

**Web客户端部署选项：**

**选项1：静态网站托管**
```bash
# 构建生产版本
cd frontend-clients/web-client
npm run build

# 部署到静态网站托管服务
# 可以是：Nginx、Apache、CDN、云存储等
cp -r dist/* /var/www/html/
```

**选项2：Docker容器部署**
```yaml
# frontend-clients/web-client/docker-compose.yml
version: '3.8'

services:
  web-client:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - API_BASE_URL=https://api.company.com
      - APP_TITLE=AI助手报表OCR
    restart: unless-stopped
```

**选项3：CDN分发**
```javascript
// 配置CDN加速
const CDN_CONFIG = {
  static_assets: 'https://cdn.company.com/ai-assistant/',
  api_endpoint: 'https://api.company.com',
  version: '1.0.0'
}
```

**桌面客户端分发：**
```bash
# 构建桌面应用
cd frontend-clients/desktop-client
npm run build:win    # Windows版本
npm run build:mac    # macOS版本
npm run build:linux  # Linux版本

# 生成安装包
# - ai-assistant-setup-1.0.0.exe (Windows)
# - ai-assistant-1.0.0.dmg (macOS)
# - ai-assistant-1.0.0.AppImage (Linux)
```

### 6.3 部署配置管理

**环境变量配置：**
```bash
# backend-service/.env.production
ENV=production
DEBUG=false

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=ai_assistant
DB_USER=ai_user
DB_PASSWORD=your_secure_password

# Redis配置
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_redis_password

# API配置
QWEN_API_URL=http://**********:8001/v1/chat/completions
QWEN_API_KEY=your_qwen_api_key

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key
JWT_EXPIRE_HOURS=24

# CORS配置
CORS_ORIGINS=https://client.company.com,https://app.company.com

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads
```

**前端配置：**
```javascript
// frontend-clients/web-client/src/config/production.js
export default {
  API_BASE_URL: 'https://api.company.com',
  API_TIMEOUT: 30000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_FORMATS: [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'application/pdf',
    'image/jpeg',
    'image/png'
  ],
  APP_CONFIG: {
    title: 'AI助手报表OCR',
    version: '1.0.0',
    company: '公司名称'
  }
}
```

## 七、前后端分离开发计划

### 7.1 第一阶段：后端API服务开发（2周）

**Week 1：核心后端服务**
- 搭建FastAPI项目结构和Docker环境
- 复用PMO的DocumentParser服务
- 实现用户认证和权限管理API
- 设计数据库模型和API数据结构
- 配置CORS、限流等安全中间件

**Week 2：业务API开发**
- 实现文件上传和解析API
- 集成千问大模型服务API
- 开发公司信息提取和隐私清理服务
- 实现任务管理和历史记录API
- API文档编写和测试

### 7.2 第二阶段：Web客户端开发（3周）

**Week 3：Web前端基础架构**
- 搭建Vue 3 + Vite + Element Plus项目
- 配置API客户端和请求拦截器
- 实现用户认证和路由守卫
- 设计响应式布局和主题系统
- 搭建状态管理和本地存储

**Week 4：核心功能开发**
- 开发文件上传组件（支持拖拽、预览）
- 实现进度显示和任务状态监控
- 开发结果展示和数据导出功能
- 实现历史记录管理界面
- 前后端API联调测试

**Week 5：Web应用完善**
- 错误处理和用户体验优化
- 移动端适配和PWA支持
- 性能优化和缓存策略
- 用户手册和帮助文档
- 生产环境部署和测试

### 7.3 第三阶段：桌面客户端开发（2周）

**Week 6-7：Electron桌面应用**
- Electron项目搭建和主进程配置
- 复用Web客户端的Vue组件和逻辑
- 实现桌面端特有功能：
  - 本地文件拖拽和系统集成
  - 系统托盘和快捷键支持
  - 自动更新和离线缓存
- 多平台打包和安装程序制作

### 7.4 第四阶段：移动客户端开发（3周）

**Week 8-10：Flutter移动应用**
- Flutter项目搭建和依赖配置
- 实现移动端UI设计和导航
- 调用后端API服务
- 移动端特有功能：
  - 相机拍照和图片选择
  - 文件管理和本地存储
  - 推送通知和后台同步
- Android/iOS打包和应用商店发布

### 7.5 第五阶段：小程序开发（2周）

**Week 11-12：微信/支付宝小程序**
- 小程序项目搭建和配置
- 实现小程序UI组件和页面
- 调用后端API（处理跨域和认证）
- 小程序特有功能：
  - 微信登录和用户授权
  - 文件上传和结果分享
  - 小程序码生成和推广
- 小程序审核和发布上线

### 7.6 第六阶段：功能扩展和优化（持续）

**后续迭代开发：**

**功能扩展：**
- 法规助手功能模块
- 更多文档格式支持（CAD图纸、音频转录等）
- AI模型优化和多模型支持
- 数据分析和可视化报表
- 批量处理和定时任务

**企业级功能：**
- 多租户和组织管理
- 角色权限和审批流程
- 数据备份和恢复
- 审计日志和合规报告
- API开放平台和第三方集成

**性能和运维：**
- 微服务架构拆分
- 容器编排和自动扩缩容
- 监控告警和日志分析
- 性能优化和缓存策略
- 安全加固和漏洞修复

### 7.7 部署和运维计划

**后端服务部署：**
- 公司内网/云服务器部署
- Docker容器化和CI/CD流水线
- 负载均衡和高可用配置
- 数据库备份和灾备方案
- 监控告警和日志收集

**前端客户端分发：**
- Web客户端：静态网站托管/CDN分发
- 桌面客户端：官网下载/企业内部分发
- 移动客户端：应用商店发布/企业应用商店
- 小程序：微信/支付宝小程序平台发布

**用户支持：**
- 用户手册和视频教程
- 在线客服和技术支持
- 用户反馈收集和产品迭代
- 培训服务和技术咨询

## 八、前后端分离架构的预期效果

### 8.1 技术架构效果

**前后端分离优势：**
1. **独立部署**：前端和后端可以独立开发、测试、部署和扩展
2. **技术选型灵活**：前端可以选择最适合的技术栈，后端专注于API服务
3. **团队协作**：前端和后端团队可以并行开发，提高开发效率
4. **维护性强**：职责分离清晰，便于维护和升级

**多端统一效果：**
1. **一套API服务**：后端API支持Web、桌面、移动、小程序等所有客户端
2. **代码复用率高**：Web组件可直接复用到Electron桌面端
3. **开发效率提升**：多端共享业务逻辑，减少重复开发
4. **维护成本降低**：统一的API接口，降低后端维护复杂度

### 8.2 功能实现效果

**文档处理能力：**
1. **多格式支持**：Excel、Word、PDF、PPT、图片等主流格式
2. **批量处理**：支持同时处理多个文件，提高工作效率
3. **识别准确率**：基于MonkeyOCR和千问大模型，准确率达到90%以上
4. **处理速度**：单个文件处理时间控制在30秒内

**AI智能分析：**
1. **公司信息提取**：自动识别公司名称、报表日期等关键信息
2. **隐私信息清理**：智能去除敏感信息，保护数据隐私
3. **标准化表格生成**：基于千问大模型生成结构化的财务数据表格
4. **数据质量保证**：多重验证机制确保数据准确性

### 8.3 用户体验效果

**多端一致体验：**
- **Web端**：响应式设计，支持桌面和移动浏览器访问
- **桌面端**：原生体验，支持本地文件拖拽和系统集成
- **移动端**：触屏优化，支持相机拍照和移动办公场景
- **小程序**：轻量级应用，即用即走，无需安装

**用户友好特性：**
1. **直观界面**：简洁明了的操作界面，降低学习成本
2. **实时反馈**：文件上传和处理进度实时显示
3. **错误处理**：友好的错误提示和处理建议
4. **离线支持**：桌面端支持离线处理，提高可用性

### 8.4 部署和运维效果

**灵活部署：**
1. **后端集中部署**：部署在公司服务器，统一管理和维护
2. **前端分布部署**：可以部署到CDN、静态网站托管等多种平台
3. **扩展性强**：支持水平扩展和负载均衡
4. **安全可控**：数据处理在公司内网，确保数据安全

**运维便利：**
1. **监控完善**：API调用监控、性能监控、错误监控
2. **日志完整**：详细的操作日志和审计跟踪
3. **备份恢复**：完善的数据备份和灾备方案
4. **版本管理**：支持API版本控制和平滑升级

### 8.5 商业价值效果

**市场竞争力：**
1. **全平台覆盖**：满足不同用户的使用习惯和场景需求
2. **技术先进性**：前后端分离架构体现技术专业性
3. **扩展能力强**：为后续法规助手等功能提供坚实基础
4. **用户粘性高**：多端协同使用，提升用户依赖度

**成本效益：**
1. **开发成本优化**：复用现有PMO技术，降低开发成本
2. **维护成本可控**：统一API接口，降低长期维护成本
3. **部署成本灵活**：前端可选择成本最优的部署方案
4. **扩展成本低**：新增客户端只需调用现有API

**风险控制：**
1. **技术风险低**：基于成熟的PMO系统技术栈
2. **数据安全**：后端部署在公司内网，数据可控
3. **服务稳定**：完善的监控和备份机制
4. **合规保证**：支持审计日志和数据合规要求

这个前后端分离的多端统一架构方案，充分利用了现有PMO系统的成熟技术和千问大模型的AI能力，通过科学的架构设计和分阶段实施，确保了技术可行性、开发效率和长期可维护性，为AI助手程序的成功实施和后续扩展奠定了坚实的基础。
