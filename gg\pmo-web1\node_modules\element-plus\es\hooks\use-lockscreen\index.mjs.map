{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-lockscreen/index.ts"], "sourcesContent": ["import { computed, isRef, onScopeDispose, watch } from 'vue'\nimport {\n  addClass,\n  getScrollBarWidth,\n  getStyle,\n  hasClass,\n  isClient,\n  removeClass,\n  throwError,\n} from '@element-plus/utils'\nimport { useNamespace } from '../use-namespace'\n\nimport type { Ref } from 'vue'\nimport type { UseNamespaceReturn } from '../use-namespace'\n\nexport type UseLockScreenOptions = {\n  ns?: UseNamespaceReturn\n  // shouldLock?: MaybeRef<boolean>\n}\n\n/**\n * Hook that monitoring the ref value to lock or unlock the screen.\n * When the trigger became true, it assumes modal is now opened and vice versa.\n * @param trigger {Ref<boolean>}\n */\nexport const useLockscreen = (\n  trigger: Ref<boolean>,\n  options: UseLockScreenOptions = {}\n) => {\n  if (!isRef(trigger)) {\n    throwError(\n      '[useLockscreen]',\n      'You need to pass a ref param to this function'\n    )\n  }\n\n  const ns = options.ns || useNamespace('popup')\n\n  const hiddenCls = computed(() => ns.bm('parent', 'hidden'))\n\n  if (!isClient || hasClass(document.body, hiddenCls.value)) {\n    return\n  }\n\n  let scrollBarWidth = 0\n  let withoutHiddenClass = false\n  let bodyWidth = '0'\n\n  const cleanup = () => {\n    setTimeout(() => {\n      // When the test case is running, the context environment simulated by jsdom may have been destroyed,\n      // and the document does not exist at this time.\n      if (typeof document === 'undefined') return\n      if (withoutHiddenClass && document) {\n        document.body.style.width = bodyWidth\n        removeClass(document.body, hiddenCls.value)\n      }\n    }, 200)\n  }\n  watch(trigger, (val) => {\n    if (!val) {\n      cleanup()\n      return\n    }\n\n    withoutHiddenClass = !hasClass(document.body, hiddenCls.value)\n    if (withoutHiddenClass) {\n      bodyWidth = document.body.style.width\n      addClass(document.body, hiddenCls.value)\n    }\n    scrollBarWidth = getScrollBarWidth(ns.namespace.value)\n    const bodyHasOverflow =\n      document.documentElement.clientHeight < document.body.scrollHeight\n    const bodyOverflowY = getStyle(document.body, 'overflowY')\n    if (\n      scrollBarWidth > 0 &&\n      (bodyHasOverflow || bodyOverflowY === 'scroll') &&\n      withoutHiddenClass\n    ) {\n      document.body.style.width = `calc(100% - ${scrollBarWidth}px)`\n    }\n  })\n  onScopeDispose(() => cleanup())\n}\n"], "names": [], "mappings": ";;;;;;;AAWY,MAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK;AACxD,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AACvB,IAAI,UAAU,CAAC,iBAAiB,EAAE,+CAA+C,CAAC,CAAC;AACnF,GAAG;AACH,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;AACjD,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC9D,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE;AAC7D,IAAI,OAAO;AACX,GAAG;AACH,EAAE,IAAI,cAAc,GAAG,CAAC,CAAC;AACzB,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACjC,EAAE,IAAI,SAAS,GAAG,GAAG,CAAC;AACtB,EAAE,MAAM,OAAO,GAAG,MAAM;AACxB,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW;AACzC,QAAQ,OAAO;AACf,MAAM,IAAI,kBAAkB,IAAI,QAAQ,EAAE;AAC1C,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAC9C,QAAQ,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACpD,OAAO;AACP,KAAK,EAAE,GAAG,CAAC,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK;AAC1B,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,kBAAkB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5C,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,cAAc,GAAG,iBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC3D,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;AAC/F,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC/D,IAAI,IAAI,cAAc,GAAG,CAAC,KAAK,eAAe,IAAI,aAAa,KAAK,QAAQ,CAAC,IAAI,kBAAkB,EAAE;AACrG,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;AACrE,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,cAAc,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;AAClC;;;;"}