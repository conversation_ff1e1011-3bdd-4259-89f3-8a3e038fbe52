{"version": 3, "file": "useFilter.mjs", "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useFilter.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { isFunction } from '@element-plus/utils'\n\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeProps } from '../types'\n\n// When the data volume is very large using filter will cause lag\n// I haven't found a better way to optimize it for now\n// Maybe this problem should be left to the server side\nexport function useFilter(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const hiddenNodeKeySet = ref<Set<TreeKey>>(new Set([]))\n  const hiddenExpandIconKeySet = ref<Set<TreeKey>>(new Set([]))\n\n  const filterable = computed(() => {\n    return isFunction(props.filterMethod)\n  })\n\n  function doFilter(query: string) {\n    if (!filterable.value) {\n      return\n    }\n    const expandKeySet = new Set<TreeKey>()\n    const hiddenExpandIconKeys = hiddenExpandIconKeySet.value\n    const hiddenKeys = hiddenNodeKeySet.value\n    const family: TreeNode[] = []\n    const nodes = tree.value?.treeNodes || []\n    const filter = props.filterMethod\n    hiddenKeys.clear()\n    function traverse(nodes: TreeNode[]) {\n      nodes.forEach((node) => {\n        family.push(node)\n        if (filter?.(query, node.data, node)) {\n          family.forEach((member) => {\n            expandKeySet.add(member.key)\n          })\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key)\n        }\n        const children = node.children\n        if (children) {\n          traverse(children)\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key)\n          } else if (children) {\n            // If all child nodes are hidden, then the expand icon will be hidden\n            let allHidden = true\n            for (const childNode of children) {\n              if (!hiddenKeys.has(childNode.key)) {\n                allHidden = false\n                break\n              }\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key)\n            } else {\n              hiddenExpandIconKeys.delete(node.key)\n            }\n          }\n        }\n        family.pop()\n      })\n    }\n    traverse(nodes)\n    return expandKeySet\n  }\n\n  function isForceHiddenExpandIcon(node: TreeNode): boolean {\n    return hiddenExpandIconKeySet.value.has(node.key)\n  }\n\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon,\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,SAAS,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE;AACvC,EAAE,MAAM,gBAAgB,GAAG,GAAG,iBAAiB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,EAAE,MAAM,sBAAsB,GAAG,GAAG,iBAAiB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC1C,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACnD,IAAI,MAAM,oBAAoB,GAAG,sBAAsB,CAAC,KAAK,CAAC;AAC9D,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,EAAE,CAAC;AAC5E,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;AACtC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;AACvB,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC9B,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,QAAQ,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;AACtE,UAAU,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACrC,YAAY,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzC,WAAW,CAAC,CAAC;AACb,SAAS,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AAChC,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,SAAS;AACT,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7B,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1B,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC3C,YAAY,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrC,WAAW,MAAM,IAAI,QAAQ,EAAE;AAC/B,YAAY,IAAI,SAAS,GAAG,IAAI,CAAC;AACjC,YAAY,KAAK,MAAM,SAAS,IAAI,QAAQ,EAAE;AAC9C,cAAc,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;AAClD,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,gBAAgB,MAAM;AACtB,eAAe;AACf,aAAa;AACb,YAAY,IAAI,SAAS,EAAE;AAC3B,cAAc,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,aAAa,MAAM;AACnB,cAAc,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpD,aAAa;AACb,WAAW;AACX,SAAS;AACT,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpB,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,SAAS,uBAAuB,CAAC,IAAI,EAAE;AACzC,IAAI,OAAO,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,OAAO;AACT,IAAI,sBAAsB;AAC1B,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,uBAAuB;AAC3B,GAAG,CAAC;AACJ;;;;"}