#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/pmo(aaanew)/pmo-web/node_modules/.store/vue-demi@0.14.10/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/pmo(aaanew)/pmo-web/node_modules/.store/vue-demi@0.14.10/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vue-demi@0.14.10/node_modules/vue-demi/bin/vue-demi-switch.js" "$@"
else
  exec node  "$basedir/../../../../../../vue-demi@0.14.10/node_modules/vue-demi/bin/vue-demi-switch.js" "$@"
fi
